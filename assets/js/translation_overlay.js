window.translationOverlays = window.translationOverlays || {
  overlays: new Map(),
  loadingOverlays: new Map(),
  actionButtons: new Map(),
  scrollHandler: null,
  intersectionObserver: null,
  visibilityObserver: null, // 图片可见性监控器
  mutationObserver: null, // DOM变化监控器（用于实时检测图片切换）
  imageCache: new Map(),
  processingImages: new Set(),
  translationModeEnabled: false,
  visibilityCheckInterval: null, // 定期可见性检查定时器
  imageStateMap: new Map(), // 图片状态映射表（URL -> 可见性状态）
  lastVisibilityCheck: 0, // 上次可见性检查时间戳

  // Site-specific configuration system
  siteLoader: null,
  currentSiteConfig: null,

  // Enhanced dynamic content loading
  dynamicContentDebounceTimer: null,
  dynamicContentQueue: [],
  isProcessingDynamicContent: false,
  recentlyNotifiedImages: new Set(),

  // Initialize site-specific configuration system
  async initializeSiteConfig() {
    console.log('TranslationOverlays: Initializing site-specific configuration system');

    try {
      // Check if site configuration classes are available
      if (typeof SiteLoader !== 'undefined' && typeof SiteConfigBase !== 'undefined') {
        // Initialize site loader
        if (!this.siteLoader) {
          this.siteLoader = new SiteLoader();
          await this.siteLoader.initialize();
        }

        // Get current site configuration
        this.currentSiteConfig = this.siteLoader.getCurrentSite();
        console.log('TranslationOverlays: Site configuration loaded:', this.currentSiteConfig.siteName);
        return true;
      } else {
        console.log('TranslationOverlays: Site configuration classes not available, using built-in logic');
        this.currentSiteConfig = null;
        return false;
      }
    } catch (error) {
      console.error('TranslationOverlays: Failed to initialize site configuration:', error);
      this.currentSiteConfig = null;
      return false;
    }
  },

  // Get current site configuration (with fallback)
  getSiteConfig() {
    // Return null if site config system is not available
    // This will trigger fallback to built-in logic
    return this.currentSiteConfig;
  },

  // Get IntersectionObserver configuration from site config or defaults
  getIntersectionObserverConfig() {
    const siteConfig = this.getSiteConfig();

    // Use site-specific configuration if available
    if (siteConfig && siteConfig.getViewportBuffer && siteConfig.getIntersectionThreshold) {
      return {
        root: null,
        rootMargin: siteConfig.getViewportBuffer(),
        threshold: siteConfig.getIntersectionThreshold()
      };
    }

    // Default configuration
    return {
      root: null,
      rootMargin: '100px',
      threshold: 0.05
    };
  },

  // Utility function to normalize image URL
  normalizeImageUrl: function(url) {
    if (!url) return url;
    return url;
  },

  // Initialize intersection observer for lazy processing
  initIntersectionObserver: function() {
    if (this.intersectionObserver) {
      // If observer already exists, re-observe all images to ensure coverage
      this.reObserveAllImages();
      return;
    }

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const img = entry.target;
        const imageUrl = this.normalizeImageUrl(img.src);

        // Filter out small icons and decorative images
        if (!this.isMainContentImage(img)) {
          return;
        }

        if (entry.isIntersecting) {
          // 图片进入视口
          console.log('主要内容图片进入视口:', imageUrl);

          // 显示翻译按钮浮层
          this.showActionButton(img);

          // 检查是否应该处理这个图片
          const shouldProcess = this.shouldProcessImage(imageUrl);

          if (shouldProcess) {
            console.log('图片需要翻译处理，通知Flutter端:', imageUrl);
            // 使用防重复通知方法
            this.notifyImageVisible(imageUrl);
          } else {
            console.log('图片不需要处理（已缓存或正在处理）:', imageUrl, {
              cached: this.imageCache.has(imageUrl),
              processing: this.processingImages.has(imageUrl)
            });
          }
        } else {
          // 图片离开视口
          // console.log('主要内容图片离开视口:', imageUrl);

          // 隐藏翻译按钮浮层
          this.hideActionButton(imageUrl);
        }
      });
    }, this.getIntersectionObserverConfig());

    // Observe only main content images
    this.observeMainContentImages();
  },

  // Observe all main content images
  observeMainContentImages: function() {
    if (!this.intersectionObserver) return;

    let observedCount = 0;
    document.querySelectorAll('img').forEach(img => {
      if (this.isMainContentImage(img)) {
        this.intersectionObserver.observe(img);
        observedCount++;
        console.log('Observing main content image:', img.src);
      }
    });
    console.log(`Observing ${observedCount} main content images`);
  },

  // Re-observe all images (useful when translation mode is re-enabled)
  reObserveAllImages: function() {
    if (!this.intersectionObserver) return;

    console.log('Re-observing all main content images...');

    // First, disconnect all existing observations
    this.intersectionObserver.disconnect();

    // Then re-observe all main content images
    this.observeMainContentImages();

    // Also manually check for immediately visible images
    this.checkImmediatelyVisibleImages();
  },

  // Check for images that are immediately visible and trigger events
  checkImmediatelyVisibleImages: function() {
    console.log('Checking for immediately visible images...');

    let immediatelyVisibleCount = 0;
    document.querySelectorAll('img').forEach(img => {
      if (this.isMainContentImage(img)) {
        const rect = img.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight &&
                         rect.bottom > 0 &&
                         rect.left < window.innerWidth &&
                         rect.right > 0;

        if (isVisible) {
          const imageUrl = this.normalizeImageUrl(img.src);
          immediatelyVisibleCount++;
          console.log('Found immediately visible image:', imageUrl);

          // Trigger visibility event after a small delay
          setTimeout(() => {
            if (this.shouldProcessImage(imageUrl)) {
              console.log('Manually triggering visibility for immediately visible image:', imageUrl);
              this.notifyImageVisible(imageUrl);
            }
          }, 50);
        }
      }
    });

    console.log(`Found ${immediatelyVisibleCount} immediately visible images`);
  },

  // 检查图片是否为主要内容（使用站点特定配置或内置逻辑）
  isMainContentImage: function(img) {
    try {
      // Use site-specific configuration if available
      const siteConfig = this.getSiteConfig();
      if (siteConfig && siteConfig.isMainContentImage) {
        const result = siteConfig.isMainContentImage(img);
        console.log('Site-specific image filter result:', result, 'for:', img.src);
        return result;
      }

      // console.log('Using built-in image filtering for:', img.src);
      return this.builtInIsMainContentImage(img);
    } catch (error) {
      console.error('Error in isMainContentImage:', error);
      // Emergency fallback
      return this.defaultIsMainContentImage(img);
    }
  },

  // Built-in image filtering logic 
  builtInIsMainContentImage: function(img) {
    if (!img || !img.src) return false;

    const url = this.normalizeImageUrl(img.src);
    // console.log('Built-in filter checking image:', url);

    // Skip data URLs
    if (url.startsWith('data:')) {
      console.log('Skipping data URL:', url);
      return false;
    }

    // Basic size filtering
    const minSize = 200;
    if (img.naturalWidth > 0 && img.naturalHeight > 0) {
      if (img.naturalWidth < minSize || img.naturalHeight < minSize) {
        console.log('Skipping small natural size image:', url, `${img.naturalWidth}x${img.naturalHeight}`);
        return false;
      }
    }

    if (img.offsetWidth > 0 && img.offsetHeight > 0) {
      if (img.offsetWidth < minSize || img.offsetHeight < minSize) {
        console.log('Skipping small displayed image:', url, `${img.offsetWidth}x${img.offsetHeight}`);
        return false;
      }
    }

    // For other sites, use default filtering
    return this.defaultIsMainContentImage(img);
  },

  // Default image filtering logic (fallback)
  defaultIsMainContentImage: function(img) {
    if (!img || !img.src) return false;

    const url = this.normalizeImageUrl(img.src);

    // Skip data URLs
    if (url.startsWith('data:')) return false;

    // Basic size filtering
    const minSize = 200;
    if (img.naturalWidth > 0 && img.naturalHeight > 0) {
      if (img.naturalWidth < minSize || img.naturalHeight < minSize) return false;
    }
    if (img.offsetWidth > 0 && img.offsetHeight > 0) {
      if (img.offsetWidth < minSize || img.offsetHeight < minSize) return false;
    }

    // Skip common decorative keywords
    const skipKeywords = ['logo', 'icon', 'favicon', 'avatar', 'thumb', 'thumbnail'];
    const urlLower = url.toLowerCase();
    const altLower = (img.alt || '').toLowerCase();

    for (const keyword of skipKeywords) {
      if (urlLower.includes(keyword) || altLower.includes(keyword)) {
        return false;
      }
    }

    return true;
  },

  // 检查图片是否在视觉上可见（考虑CSS样式）
  isImageVisuallyVisible: function(img) {
    if (!img || !img.parentNode) {
      return false;
    }

    // 检查图片本身的可见性
    const imgStyle = window.getComputedStyle(img);
    if (imgStyle.display === 'none' ||
        imgStyle.visibility === 'hidden' ||
        imgStyle.opacity === '0') {
      return false;
    }

    // 检查父元素的可见性（递归向上检查）
    let parent = img.parentElement;
    while (parent && parent !== document.body) {
      const parentStyle = window.getComputedStyle(parent);
      if (parentStyle.display === 'none' ||
          parentStyle.visibility === 'hidden' ||
          parentStyle.opacity === '0') {
        return false;
      }
      parent = parent.parentElement;
    }

    // 检查图片是否在视口内（真正的视口检查）
    const rect = img.getBoundingClientRect();

    // 首先检查图片是否有有效尺寸
    if (rect.width <= 0 || rect.height <= 0) {
      return false;
    }

    // 检查图片是否在视口内（至少部分可见）
    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

    const isInViewport = (
      rect.bottom > 0 &&           // 图片底部在视口顶部之下
      rect.right > 0 &&            // 图片右边在视口左边之右
      rect.top < viewportHeight &&  // 图片顶部在视口底部之上
      rect.left < viewportWidth     // 图片左边在视口右边之左
    );

    // 额外检查：图片是否有足够的可见面积（至少10%可见）
    if (isInViewport) {
      const visibleTop = Math.max(0, rect.top);
      const visibleLeft = Math.max(0, rect.left);
      const visibleBottom = Math.min(viewportHeight, rect.bottom);
      const visibleRight = Math.min(viewportWidth, rect.right);

      const visibleWidth = Math.max(0, visibleRight - visibleLeft);
      const visibleHeight = Math.max(0, visibleBottom - visibleTop);
      const visibleArea = visibleWidth * visibleHeight;
      const totalArea = rect.width * rect.height;

      const visibilityRatio = totalArea > 0 ? visibleArea / totalArea : 0;

      // 要求至少10%的图片面积可见
      const isSignificantlyVisible = visibilityRatio >= 0.1;

      console.log(`图片可见性检查: ${img.src}`, {
        rect: { top: rect.top, left: rect.left, width: rect.width, height: rect.height },
        viewport: { width: viewportWidth, height: viewportHeight },
        visibleArea: { width: visibleWidth, height: visibleHeight },
        visibilityRatio: visibilityRatio.toFixed(3),
        isSignificantlyVisible: isSignificantlyVisible
      });

      return isSignificantlyVisible;
    }

    return false;
  },

  // 判断图片是否应该被处理（综合检查）
  shouldProcessImage: function(imageUrl) {
    // 检查是否正在处理（但允许一定的容错）
    if (this.processingImages.has(imageUrl)) {
      // 检查处理时间，如果超过30秒则认为可能卡住了，允许重新处理
      const now = Date.now();
      const imageState = this.imageStateMap.get(imageUrl);
      if (imageState && imageState.lastChecked && (now - imageState.lastChecked > 30000)) {
        console.log('图片处理可能卡住，允许重新处理:', imageUrl);
        this.processingImages.delete(imageUrl);
        return true;
      }
      return false;
    }

    // 检查图片可见性状态
    const imageState = this.imageStateMap.get(imageUrl);
    if (imageState && !imageState.isVisible) {
      return false;
    }

    // 注意：移除了缓存检查，让Flutter端决定如何处理缓存图片
    // 这样缓存的图片也会触发可见事件，Flutter端可以直接显示缓存结果
    return true;
  },

  // 防重复通知图片可见的方法
  notifyImageVisible: function(imageUrl) {
    // 检查是否最近已经通知过这张图片
    if (this.recentlyNotifiedImages && this.recentlyNotifiedImages.has(imageUrl)) {
      console.log('图片最近已通知过，跳过重复通知:', imageUrl);
      return;
    }

    // 添加到最近通知集合
    if (!this.recentlyNotifiedImages) {
      this.recentlyNotifiedImages = new Set();
    }
    this.recentlyNotifiedImages.add(imageUrl);

    // 设置5秒后清除，允许重新通知
    setTimeout(() => {
      if (this.recentlyNotifiedImages) {
        this.recentlyNotifiedImages.delete(imageUrl);
      }
    }, 5000);

    // 发送通知
    console.log('通知Flutter端图片可见:', imageUrl);
    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
      window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
    }
  },

  // 初始化图片可见性监控（改进版：实时检测 + 定期检查）
  initVisibilityMonitoring: function() {
    console.log('正在启动图片可见性监控系统...');

    // 1. 停止现有监控
    this.stopVisibilityMonitoring();

    // 2. 初始化图片状态映射和防重复通知集合
    this.initImageStateMap();
    this.recentlyNotifiedImages = new Set();

    // 3. 启动MutationObserver实时监控
    this.initMutationObserver();

    // 4. 启动定期检查作为备用机制（提高频率以更快清理隐藏图片）
    this.visibilityCheckInterval = setInterval(() => {
      this.performPeriodicVisibilityCheck();
    }, 1000); // 每1秒检查一次，更快响应图片切换

    console.log('图片可见性监控系统已启动（实时检测 + 定期备用检查）');
  },

  // 初始化图片状态映射表
  initImageStateMap: function() {
    this.imageStateMap.clear();

    // 记录当前所有图片的可见性状态
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (this.isMainContentImage(img)) {
        const imageUrl = this.normalizeImageUrl(img.src);
        const isVisible = this.isImageVisuallyVisible(img);
        this.imageStateMap.set(imageUrl, {
          isVisible: isVisible,
          lastChecked: Date.now(),
          hasOverlay: this.hasImageOverlays(imageUrl),
          element: img
        });
      }
    });

    console.log(`已初始化 ${this.imageStateMap.size} 个图片的状态映射`);
  },

  // 初始化DOM变化监控器（实时检测图片切换）
  initMutationObserver: function() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    this.mutationObserver = new MutationObserver((mutations) => {
      this.handleDOMChanges(mutations);
    });

    // 监控配置：关注属性变化（特别是style属性）和子节点变化
    const config = {
      attributes: true,
      attributeFilter: ['style', 'class', 'hidden'], // 监控样式和类名变化
      childList: true, // 监控子节点添加/删除
      subtree: true, // 监控整个子树
      attributeOldValue: true // 保留旧属性值用于比较
    };

    this.mutationObserver.observe(document.body, config);
    console.log('DOM变化监控器已启动，监控图片切换事件');
  },

  // Enhanced DOM changes handler with improved infinite scroll support
  handleDOMChanges: function(mutations) {
    // Add mutations to queue for debounced processing
    this.dynamicContentQueue.push(...mutations);

    // Clear existing timer
    if (this.dynamicContentDebounceTimer) {
      clearTimeout(this.dynamicContentDebounceTimer);
    }

    // Get debounce delay from site configuration or use built-in logic
    const siteConfig = this.getSiteConfig();
    let debounceDelay = 300; // Default delay

    if (siteConfig && siteConfig.getDynamicContentDebounceDelay) {
      debounceDelay = siteConfig.getDynamicContentDebounceDelay();
    }

    // Set new timer for debounced processing
    this.dynamicContentDebounceTimer = setTimeout(() => {
      this.processDynamicContentQueue();
    }, debounceDelay);
  },

  // Process queued dynamic content changes
  processDynamicContentQueue: function() {
    if (this.isProcessingDynamicContent || this.dynamicContentQueue.length === 0) {
      return;
    }

    this.isProcessingDynamicContent = true;
    console.log('Processing dynamic content queue:', this.dynamicContentQueue.length, 'mutations');

    try {
      const mutations = [...this.dynamicContentQueue];
      this.dynamicContentQueue = []; // Clear queue

      // Let site-specific configuration handle the mutations first (if available)
      const siteConfig = this.getSiteConfig();
      if (siteConfig && siteConfig.handleDynamicContent) {
        siteConfig.handleDynamicContent(mutations);
      }

      // Process mutations for image changes
      this.processImageMutations(mutations);

    } catch (error) {
      console.error('Error processing dynamic content queue:', error);
    } finally {
      this.isProcessingDynamicContent = false;
    }
  },

  // Process mutations specifically for image changes
  processImageMutations: function(mutations) {
    const newImages = new Set();
    const changedImages = new Set();
    let hasSignificantChanges = false;

    mutations.forEach(mutation => {
      // Handle attribute changes (style, class, etc.)
      if (mutation.type === 'attributes') {
        const target = mutation.target;

        if (target.tagName === 'IMG') {
          if (this.isMainContentImage(target)) {
            changedImages.add(target);
            hasSignificantChanges = true;
          }
        } else {
          // Check for images in modified containers
          const images = target.querySelectorAll ? target.querySelectorAll('img') : [];
          images.forEach(img => {
            if (this.isMainContentImage(img)) {
              changedImages.add(img);
              hasSignificantChanges = true;
            }
          });
        }
      }

      // Handle node additions (new content loading)
      else if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Direct image additions
            if (node.tagName === 'IMG') {
              if (this.isMainContentImage(node)) {
                newImages.add(node);
                hasSignificantChanges = true;
                console.log('New image detected via dynamic loading:', node.src);
              }
            } else {
              // Images within added containers (infinite scroll, pagination)
              const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
              images.forEach(img => {
                if (this.isMainContentImage(img)) {
                  newImages.add(img);
                  hasSignificantChanges = true;
                  console.log('New image in container detected:', img.src);
                }
              });
            }
          }
        });
      }
    });

    if (hasSignificantChanges) {
      console.log(`Dynamic content processing: ${newImages.size} new images, ${changedImages.size} changed images`);

      // Process new images
      if (newImages.size > 0) {
        this.handleNewDynamicImages(Array.from(newImages));
      }

      // Process changed images
      if (changedImages.size > 0) {
        this.handleChangedDynamicImages(Array.from(changedImages));
      }
    }
  },

  // Handle newly detected images from dynamic content loading
  handleNewDynamicImages: function(newImages) {
    console.log('Handling', newImages.length, 'new dynamic images');

    newImages.forEach(img => {
      const imageUrl = this.normalizeImageUrl(img.src);

      // Add to IntersectionObserver for monitoring
      if (this.intersectionObserver) {
        this.intersectionObserver.observe(img);
        console.log('Added new image to IntersectionObserver:', imageUrl);
      }

      // Initialize state tracking
      this.imageStateMap.set(imageUrl, {
        isVisible: this.isImageVisuallyVisible(img),
        lastChecked: Date.now(),
        hasOverlay: false,
        element: img
      });

      // If image is immediately visible and translation mode is enabled, show button
      if (this.translationModeEnabled && this.isImageVisuallyVisible(img)) {
        console.log('New image is immediately visible, showing action button:', imageUrl);
        this.showActionButton(img);
      }
    });
  },

  // Handle changes to existing images
  handleChangedDynamicImages: function(changedImages) {
    console.log('Handling', changedImages.length, 'changed dynamic images');

    changedImages.forEach(img => {
      const imageUrl = this.normalizeImageUrl(img.src);
      const currentVisibility = this.isImageVisuallyVisible(img);
      const previousState = this.imageStateMap.get(imageUrl);

      if (previousState && previousState.isVisible !== currentVisibility) {
        console.log(`Image visibility changed: ${imageUrl}, ${previousState.isVisible} -> ${currentVisibility}`);

        // Update state
        previousState.isVisible = currentVisibility;
        previousState.lastChecked = Date.now();

        if (this.translationModeEnabled) {
          if (currentVisibility) {
            // Image became visible - show button
            this.showActionButton(img);
          } else {
            // Image became hidden - hide button
            this.hideActionButton(imageUrl);
          }
        }
      }
    });
  },

  // 检查发生变化的图片
  checkChangedImages: function(changedImages) {
    const imagesToClean = [];
    const imagesToUpdate = [];

    changedImages.forEach(img => {
      const imageUrl = this.normalizeImageUrl(img.src);
      const currentVisibility = this.isImageVisuallyVisible(img);
      const previousState = this.imageStateMap.get(imageUrl);

      if (previousState) {
        // 检查可见性是否发生变化
        if (previousState.isVisible !== currentVisibility) {
          console.log(`图片可见性发生变化: ${imageUrl}, ${previousState.isVisible} -> ${currentVisibility}`);

          if (!currentVisibility && previousState.hasOverlay) {
            // 图片从可见变为不可见，且有overlay，需要清理
            imagesToClean.push(imageUrl);
          } else if (currentVisibility && !previousState.isVisible) {
            // 图片从不可见变为可见，更新状态
            imagesToUpdate.push(imageUrl);
          }
        }
      } else {
        // 新图片，添加到状态映射
        this.imageStateMap.set(imageUrl, {
          isVisible: currentVisibility,
          lastChecked: Date.now(),
          hasOverlay: this.hasImageOverlays(imageUrl),
          element: img
        });

        if (currentVisibility) {
          console.log(`发现新的可见图片: ${imageUrl}`);
        }

        // 将新图片添加到IntersectionObserver监控
        if (this.intersectionObserver && this.isMainContentImage(img)) {
          this.intersectionObserver.observe(img);
          console.log(`新图片已添加到IntersectionObserver监控: ${imageUrl}`);
        }
      }

      // 更新状态映射
      if (previousState) {
        previousState.isVisible = currentVisibility;
        previousState.lastChecked = Date.now();
        previousState.element = img;
      }
    });

    // 执行清理操作
    imagesToClean.forEach(imageUrl => {
      console.log('实时检测到图片隐藏，立即清理:', imageUrl);
      this.cleanupImageCompletely(imageUrl);

      // 更新状态映射中的overlay状态
      const state = this.imageStateMap.get(imageUrl);
      if (state) {
        state.hasOverlay = false;
      }
    });

    // 通知新图片可见（使用统一的检查逻辑）
    imagesToUpdate.forEach(imageUrl => {
      console.log('检测到图片重新可见:', imageUrl);

      // 使用统一的处理检查逻辑
      if (this.shouldProcessImage(imageUrl)) {
        console.log('图片需要处理，通知Flutter端:', imageUrl);
        this.notifyImageVisible(imageUrl);
      } else {
        console.log('图片不需要处理，跳过通知:', imageUrl);
      }
    });

    if (imagesToClean.length > 0 || imagesToUpdate.length > 0) {
      console.log(`实时检测完成: 清理了 ${imagesToClean.length} 个隐藏图片, 发现 ${imagesToUpdate.length} 个新可见图片`);
    }
  },

  // 快速清理隐藏图片（用于图片切换时的即时清理）
  quickCleanupHiddenImages: function() {
    const imagesToClean = [];

    // 快速检查所有有overlay的图片
    this.overlays.forEach((overlayData, overlayId) => {
      const imageUrl = overlayData.imageUrl;
      if (imageUrl && !this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 立即清理隐藏的图片
    imagesToClean.forEach(imageUrl => {
      console.log('快速清理隐藏图片:', imageUrl);
      this.cleanupImageCompletely(imageUrl);
    });

    if (imagesToClean.length > 0) {
      console.log(`快速清理完成: 清理了 ${imagesToClean.length} 个隐藏图片`);
    }
  },

  // 定期可见性检查（作为备用机制）
  performPeriodicVisibilityCheck: function() {
    console.log('执行定期可见性检查（备用机制）');

    const imagesToClean = [];
    const now = Date.now();

    // 检查状态映射中的所有图片
    this.imageStateMap.forEach((state, imageUrl) => {
      // 跳过最近已检查的图片（减少间隔以提高响应速度）
      if (now - state.lastChecked < 1000) {
        return;
      }

      const currentVisibility = this.isImageStillVisible(imageUrl);

      if (state.isVisible !== currentVisibility) {
        console.log(`定期检查发现可见性变化: ${imageUrl}, ${state.isVisible} -> ${currentVisibility}`);

        if (!currentVisibility && state.hasOverlay) {
          imagesToClean.push(imageUrl);
        }

        // 更新状态
        state.isVisible = currentVisibility;
        state.lastChecked = now;
        if (!currentVisibility) {
          state.hasOverlay = false;
        }
      }
    });

    // 执行清理
    imagesToClean.forEach(imageUrl => {
      console.log('定期检查发现隐藏图片，执行清理:', imageUrl);
      this.cleanupImageCompletely(imageUrl);
    });

    // 同时执行传统的全面检查（降低频率）
    if (now % 15000 < 5000) { // 每15秒执行一次全面检查
      this.checkAndCleanHiddenImages();
    }
  },

  // 停止图片可见性监控
  stopVisibilityMonitoring: function() {
    console.log('正在停止图片可见性监控系统...');

    // 停止定期检查
    if (this.visibilityCheckInterval) {
      clearInterval(this.visibilityCheckInterval);
      this.visibilityCheckInterval = null;
    }

    // 停止DOM变化监控
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    // 清理状态映射和防重复通知集合
    this.imageStateMap.clear();
    if (this.recentlyNotifiedImages) {
      this.recentlyNotifiedImages.clear();
    }

    console.log('图片可见性监控系统已完全停止');
  },

  // 检查并清理隐藏图片的相关内容
  checkAndCleanHiddenImages: function() {
    const imagesToClean = [];

    // 检查所有有overlay的图片
    this.overlays.forEach((overlayData, overlayId) => {
      const imageUrl = overlayData.imageUrl;
      if (imageUrl && !this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 检查所有有加载指示器的图片
    this.loadingOverlays.forEach((loadingData, imageUrl) => {
      if (!this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 检查所有有操作按钮的图片
    this.actionButtons.forEach((button, imageUrl) => {
      if (!this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 去重并清理
    const uniqueImagesToClean = [...new Set(imagesToClean)];
    uniqueImagesToClean.forEach(imageUrl => {
      console.log('检测到隐藏图片，正在清理相关内容:', imageUrl);
      this.cleanupImageCompletely(imageUrl);
    });

    if (uniqueImagesToClean.length > 0) {
      console.log(`已清理 ${uniqueImagesToClean.length} 个隐藏图片的相关内容`);
    }
  },

  // 检查指定URL的图片是否仍然可见
  isImageStillVisible: function(imageUrl) {
    const images = document.querySelectorAll('img');
    for (const img of images) {
      if (this.normalizeImageUrl(img.src) === imageUrl) {
        return this.isImageVisuallyVisible(img);
      }
    }
    return false; // 图片不存在或不可见
  },

  // 完全清理图片的所有相关内容
  cleanupImageCompletely: function(imageUrl) {
    try {
      // 1. 移除翻译overlay
      const overlaysRemoved = this.removeImageOverlays(imageUrl);

      // 2. 移除加载指示器
      this.hideLoadingIndicator(imageUrl);

      // 3. 移除缓存指示器
      this.hideCacheIndicator(imageUrl);

      // 4. 移除操作按钮
      this.removeActionButton(imageUrl);

      // 5. 清理缓存状态
      this.imageCache.delete(imageUrl);
      this.processingImages.delete(imageUrl);

      console.log(`已完全清理图片相关内容: ${imageUrl}, 移除了 ${overlaysRemoved ? '翻译overlay' : '无overlay'}`);

      // 6. 通知Flutter端图片已隐藏
      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('onImageHidden', imageUrl);
      }

      return true;
    } catch (error) {
      console.error('清理图片内容时发生错误:', imageUrl, error);
      return false;
    }
  },

  // Show loading indicator on image
  showLoadingIndicator: function(imageUrl, loadingText) {
    // Check if loading indicator already exists for this image
    if (this.loadingOverlays.has(imageUrl)) {
      console.log('Loading indicator already exists for:', imageUrl);
      return; // Don't create duplicate loading indicators
    }

    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        break;
      }
    }

    if (!targetImage) return;

    this.processingImages.add(imageUrl);

    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loading-overlay-' + Date.now();
    loadingOverlay.className = 'translation-loading-overlay';

    // Create spinner and text
    const spinner = document.createElement('div');
    spinner.className = 'translation-loading-spinner';

    const text = document.createElement('div');
    text.textContent = loadingText || 'Translating...';

    loadingOverlay.appendChild(spinner);
    loadingOverlay.appendChild(text);

    // Cover the entire image with semi-transparent overlay - ensure proper positioning
    const imageLeft = imageRect.left + scrollX;
    const imageTop = imageRect.top + scrollY;
    const imageWidth = Math.max(imageRect.width, 120); // Minimum width
    const imageHeight = Math.max(imageRect.height, 80); // Minimum height

    loadingOverlay.style.position = 'absolute';
    loadingOverlay.style.left = imageLeft + 'px';
    loadingOverlay.style.top = imageTop + 'px';
    loadingOverlay.style.width = imageWidth + 'px';
    loadingOverlay.style.height = imageHeight + 'px';
    loadingOverlay.style.zIndex = '10001';

    // Ensure the overlay is visible and properly styled
    loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    loadingOverlay.style.color = 'white';
    loadingOverlay.style.display = 'flex';
    loadingOverlay.style.alignItems = 'center';
    loadingOverlay.style.justifyContent = 'center';
    loadingOverlay.style.flexDirection = 'column';
    loadingOverlay.style.borderRadius = '8px';
    loadingOverlay.style.border = '2px solid rgba(255, 255, 255, 0.3)';
    loadingOverlay.style.opacity = '1';
    loadingOverlay.style.visibility = 'visible';

    document.body.appendChild(loadingOverlay);
    this.loadingOverlays.set(imageUrl, loadingOverlay);

    console.log('Loading overlay created:', {
      id: loadingOverlay.id,
      position: { left: loadingOverlay.style.left, top: loadingOverlay.style.top },
      size: { width: loadingOverlay.style.width, height: loadingOverlay.style.height },
      zIndex: loadingOverlay.style.zIndex,
      imageUrl: imageUrl,
      imageRect: { left: imageRect.left, top: imageRect.top, width: imageRect.width, height: imageRect.height },
      scroll: { x: scrollX, y: scrollY },
      finalPosition: { left: imageLeft, top: imageTop, width: imageWidth, height: imageHeight }
    });

    // Set a timeout to auto-hide loading indicator after 30 seconds
    setTimeout(() => {
      if (this.loadingOverlays.has(imageUrl)) {
        console.log('Auto-hiding loading indicator after timeout for:', imageUrl);
        this.hideLoadingIndicator(imageUrl);
      }
    }, 30000); // 30 seconds timeout

    console.log('Loading indicator shown for:', imageUrl);
  },

  // Hide loading indicator
  hideLoadingIndicator: function(imageUrl) {
    console.log('Attempting to hide loading indicator for:', imageUrl);

    // Try exact match first
    let loadingOverlay = this.loadingOverlays.get(imageUrl);
    if (loadingOverlay) {
      loadingOverlay.remove();
      this.loadingOverlays.delete(imageUrl);
      this.processingImages.delete(imageUrl);
      console.log('Loading indicator hidden for:', imageUrl);
      return;
    }

    // Try partial match for blob URLs
    const images = document.querySelectorAll('img');
    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);
      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        // Find and remove loading overlay for this image
        this.loadingOverlays.forEach((overlay, key) => {
          if (key === imageUrl || key.includes(imageUrl.split('/').pop())) {
            overlay.remove();
            this.loadingOverlays.delete(key);
            this.processingImages.delete(key);
            console.log('Loading indicator hidden for partial match:', key);
          }
        });
        break;
      }
    }
  },

  // Force hide all loading indicators (cleanup function)
  hideAllLoadingIndicators: function() {
    console.log('Hiding all loading indicators, count:', this.loadingOverlays.size);
    this.loadingOverlays.forEach((overlay, imageUrl) => {
      console.log('Removing loading overlay for:', imageUrl);
      overlay.remove();
      this.processingImages.delete(imageUrl);
    });
    this.loadingOverlays.clear();
  },

  // Debug loading indicators status
  debugLoadingIndicators: function() {
    const loadingOverlays = document.querySelectorAll('.translation-loading-overlay');
    const debugInfo = {
      mapSize: this.loadingOverlays.size,
      domElements: loadingOverlays.length,
      processingImages: Array.from(this.processingImages),
      overlayDetails: Array.from(loadingOverlays).map((overlay, index) => {
        const rect = overlay.getBoundingClientRect();
        return {
          id: overlay.id,
          className: overlay.className,
          position: {
            left: overlay.style.left,
            top: overlay.style.top,
            width: overlay.style.width,
            height: overlay.style.height
          },
          computed: {
            left: rect.left,
            top: rect.top,
            width: rect.width,
            height: rect.height
          },
          visible: rect.width > 0 && rect.height > 0,
          zIndex: overlay.style.zIndex,
          opacity: window.getComputedStyle(overlay).opacity
        };
      })
    };
    console.log('Loading indicators debug:', debugInfo);
    return debugInfo;
  },

  // Create and display overlays for translated text elements
  createImageOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
    console.log('=== Creating overlay ===');
    console.log('ID:', id);
    console.log('Text:', text);
    console.log('ImageURL:', imageUrl);
    console.log('Position:', x, y, width, height);
    console.log('Current overlays count:', this.overlays.size);

    // Check if overlay with this ID already exists in Map
    if (this.overlays.has(id)) {
      console.log('WARNING: Overlay with ID', id, 'already exists in Map, skipping creation');
      console.log('Existing overlay:', this.overlays.get(id));
      return this.overlays.get(id).element;
    }

    // Also check if DOM element with this ID already exists
    const existingDomElement = document.getElementById('translation-overlay-' + id);
    if (existingDomElement) {
      console.log('WARNING: DOM element with ID translation-overlay-' + id + ' already exists, removing it');
      existingDomElement.remove();
    }

    // Hide loading indicator for this specific image only (not all)
    console.log('Hiding loading indicator for image:', imageUrl);
    this.hideLoadingIndicator(imageUrl);

    // Find the image element
    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      console.log('Checking image:', img.src);
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        console.log('Found target image:', img.src);
        break;
      }
    }

    if (!targetImage) {
      console.warn('Target image not found for overlay:', imageUrl);
      console.log('Available images:', Array.from(images).map(img => img.src));
      // Still hide loading indicator even if image not found
      this.hideLoadingIndicator(imageUrl);
      // Fallback to absolute positioning
      return this.createOverlay(id, text, imageUrl, x, y, width, height, fontSize);
    }

    // Wait for image to load if necessary
    if (!targetImage.complete || targetImage.naturalWidth === 0) {
      console.log('Image not loaded yet, waiting...');
      targetImage.onload = () => {
        this.createImageOverlay(id, text, imageUrl, x, y, width, height, fontSize);
      };
      // Set a timeout to hide loading indicator if image fails to load
      setTimeout(() => {
        this.hideLoadingIndicator(imageUrl);
      }, 10000); // 10 second timeout
      return null;
    }

    // Get image position and size
    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    console.log('Image rect:', imageRect);
    console.log('Natural size:', targetImage.naturalWidth, targetImage.naturalHeight);
    console.log('Scroll position:', scrollX, scrollY);

    // Calculate scale factors for coordinate transformation
    const scaleX = imageRect.width / targetImage.naturalWidth;
    const scaleY = imageRect.height / targetImage.naturalHeight;

    console.log('Scale factors:', { scaleX, scaleY });
    console.log('Image dimensions:', {
      natural: { width: targetImage.naturalWidth, height: targetImage.naturalHeight },
      displayed: { width: imageRect.width, height: imageRect.height }
    });

    // OCR coordinates are relative to the natural image size
    // Transform them to the displayed image coordinates
    const scaledX = x * scaleX;
    const scaledY = y * scaleY;

    // Calculate absolute position on the page
    // Add image's position on page plus the scaled OCR coordinates
    const absoluteX = imageRect.left + scrollX + scaledX;
    const absoluteY = imageRect.top + scrollY + scaledY;

    console.log('Position calculations:', {
      ocrCoordinates: { x, y },
      scaledCoordinates: { x: scaledX, y: scaledY },
      imagePosition: { left: imageRect.left, top: imageRect.top },
      scroll: { x: scrollX, y: scrollY },
      finalAbsolute: { x: absoluteX, y: absoluteY }
    });

    // Ensure overlay stays within reasonable bounds
    // Allow some negative positioning for overlays that extend beyond image bounds
    const finalX = Math.max(-100, absoluteX); // Allow 100px negative offset
    const finalY = Math.max(-50, absoluteY);  // Allow 50px negative offset

    console.log('Final position after bounds check:', { x: finalX, y: finalY });
    console.log('Position calculation debug:', {
      originalAbsolute: { x: absoluteX, y: absoluteY },
      afterBoundsCheck: { x: finalX, y: finalY },
      wasClampedToZero: { x: absoluteX < 0, y: absoluteY < 0 }
    });

    const overlay = document.createElement('div');
    overlay.id = 'translation-overlay-' + id;
    overlay.className = 'translation-overlay';
    overlay.textContent = text;
    overlay.style.left = finalX + 'px';
    overlay.style.top = finalY + 'px';
    overlay.style.fontSize = fontSize + 'px';

    // Set overlay dimensions - use scaled dimensions but ensure minimum visibility
    const overlayWidth = Math.max(width * scaleX, 80); // Minimum width of 80px
    const overlayHeight = Math.max(height * scaleY, fontSize * 1.4); // Minimum height based on font size with proper line height

    overlay.style.width = overlayWidth + 'px';
    overlay.style.height = overlayHeight + 'px';
    overlay.style.maxWidth = 'none';
    overlay.style.minWidth = '80px';
    overlay.style.minHeight = (fontSize * 1.4) + 'px';
    overlay.style.lineHeight = '1.4';
    overlay.style.zIndex = '10000';

    // Ensure proper styling for visibility and readability
    overlay.style.position = 'absolute';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    overlay.style.color = '#333';
    overlay.style.border = '1px solid rgba(0,0,0,0.1)';
    overlay.style.borderRadius = '4px';
    overlay.style.padding = '4px 6px';
    overlay.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
    overlay.style.opacity = '1';
    overlay.style.visibility = 'visible';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.textAlign = 'center';
    overlay.style.fontWeight = '500';

    console.log('Overlay dimensions set:', {
      width: overlayWidth,
      height: overlayHeight,
      originalOcrWidth: width,
      originalOcrHeight: height,
      scaleX: scaleX,
      scaleY: scaleY
    });

    // Ensure text wraps properly within the overlay area (use modern CSS)
    overlay.style.overflowWrap = 'break-word';
    overlay.style.whiteSpace = 'normal';
    overlay.style.overflow = 'hidden';

    // Add the overlay to the DOM first
    document.body.appendChild(overlay);

    // Store overlay data with corrected coordinates
    this.overlays.set(id, {
      element: overlay,
      targetImage: targetImage,
      originalX: x, // Store original OCR coordinates
      originalY: y,
      scrollX: scrollX,
      scrollY: scrollY,
      imageUrl: imageUrl
    });

    // Mark image as processed in cache
    this.imageCache.set(imageUrl, true);

    console.log('=== Overlay creation completed ===');
    console.log('Overlay ID:', id);
    console.log('Overlay element added to DOM:', overlay);
    console.log('Overlay position in DOM:', {
      left: overlay.style.left,
      top: overlay.style.top,
      width: overlay.style.width,
      height: overlay.style.height,
      zIndex: overlay.style.zIndex,
      opacity: overlay.style.opacity
    });
    console.log('Total overlays now:', this.overlays.size);
    console.log('All overlay IDs:', Array.from(this.overlays.keys()));

    // Update action button state to completed after overlay is created
    // Use forceUpdate to ensure state changes even if overlay check is delayed
    this.updateActionButtonState(imageUrl, 'completed', true);

    // Fade in animation
    setTimeout(() => {
      overlay.classList.add('fade-in');
      console.log('Fade in animation applied for overlay:', id);
    }, 10);

    return overlay;
  },

  createOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
    // Check if overlay with this ID already exists
    if (this.overlays.has(id)) {
      console.log('Overlay with ID', id, 'already exists, skipping creation');
      return this.overlays.get(id).element;
    }

    // Fallback to absolute positioning
    const overlay = document.createElement('div');
    overlay.id = 'translation-overlay-' + id;
    overlay.className = 'translation-overlay';
    overlay.textContent = text;
    overlay.style.left = x + 'px';
    overlay.style.top = y + 'px';
    overlay.style.fontSize = fontSize + 'px';

    // Set dimensions to match OCR detected area
    const overlayWidth = Math.max(width, 100); // Minimum width of 100px
    const overlayHeight = Math.max(height, fontSize * 1.4); // Minimum height based on font size with proper line height

    overlay.style.width = overlayWidth + 'px';
    overlay.style.height = overlayHeight + 'px';
    overlay.style.minWidth = Math.min(overlayWidth, 100) + 'px';
    overlay.style.maxWidth = 'none';
    overlay.style.lineHeight = '1.4';

    // Apply consistent styling
    overlay.style.position = 'absolute';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    overlay.style.color = '#333';
    overlay.style.border = '1px solid rgba(0,0,0,0.1)';
    overlay.style.borderRadius = '4px';
    overlay.style.padding = '4px 6px';
    overlay.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
    overlay.style.zIndex = '10000';
    overlay.style.opacity = '1';
    overlay.style.visibility = 'visible';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.textAlign = 'center';
    overlay.style.fontWeight = '500';

    // Ensure text wraps properly within the overlay area
    overlay.style.overflowWrap = 'break-word';
    overlay.style.whiteSpace = 'normal';
    overlay.style.overflow = 'hidden';

    document.body.appendChild(overlay);
    this.overlays.set(id, {
      element: overlay,
      originalX: x,
      originalY: y,
      scrollX: window.scrollX,
      scrollY: window.scrollY,
      imageUrl: imageUrl
    });

    // Update action button state to completed
    // Use forceUpdate to ensure state changes even if overlay check is delayed
    this.updateActionButtonState(imageUrl, 'completed', true);

    // Fade in animation
    setTimeout(() => overlay.classList.add('fade-in'), 10);

    return overlay;
  },
  
  removeOverlay: function(id) {
    const overlayData = this.overlays.get(id);
    if (overlayData) {
      overlayData.element.classList.add('fade-out');
      setTimeout(() => {
        if (overlayData.element.parentNode) {
          overlayData.element.parentNode.removeChild(overlayData.element);
        }
        this.overlays.delete(id);
      }, 200);
    }
  },
  
  removeAllOverlays: function() {
    // Collect all image URLs that have overlays before removing them
    const imageUrlsWithOverlays = new Set();
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl) {
        imageUrlsWithOverlays.add(overlayData.imageUrl);
      }
      this.removeOverlay(id);
    });
    this.overlays.clear();

    // Reset action button states for images that had overlays
    imageUrlsWithOverlays.forEach(imageUrl => {
      this.updateActionButtonState(imageUrl, 'ready');
    });

    console.log('Removed all overlays and reset', imageUrlsWithOverlays.size, 'action button states');
  },
  
  updateOverlayPositions: function() {
    const currentScrollX = window.scrollX;
    const currentScrollY = window.scrollY;

    this.overlays.forEach((overlayData) => {
      if (overlayData.targetImage) {
        // Image-relative positioning with corrected coordinate transformation
        const imageRect = overlayData.targetImage.getBoundingClientRect();
        const scaleX = imageRect.width / overlayData.targetImage.naturalWidth;
        const scaleY = imageRect.height / overlayData.targetImage.naturalHeight;

        // Transform OCR coordinates to current display coordinates
        const scaledX = overlayData.originalX * scaleX;
        const scaledY = overlayData.originalY * scaleY;

        const absoluteX = imageRect.left + currentScrollX + scaledX;
        const absoluteY = imageRect.top + currentScrollY + scaledY;

        overlayData.element.style.left = Math.max(-100, absoluteX) + 'px';
        overlayData.element.style.top = Math.max(-50, absoluteY) + 'px';
      } else {
        // Fallback to absolute positioning
        const deltaX = currentScrollX - overlayData.scrollX;
        const deltaY = currentScrollY - overlayData.scrollY;

        overlayData.element.style.left = (overlayData.originalX - deltaX) + 'px';
        overlayData.element.style.top = (overlayData.originalY - deltaY) + 'px';
      }
    });
  },
  
  setupScrollHandler: function() {
    if (this.scrollHandler) return;
    
    this.scrollHandler = () => {
      this.updateOverlayPositions();
    };
    
    window.addEventListener('scroll', this.scrollHandler, { passive: true });
    window.addEventListener('resize', this.scrollHandler, { passive: true });
  },
  
  removeScrollHandler: function() {
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
      window.removeEventListener('resize', this.scrollHandler);
      this.scrollHandler = null;
    }
  },

  // Initialize the system with site-specific configuration
  async initialize() {
    console.log('Translation overlay system: Starting initialization');

    try {
      // Try to initialize site-specific configuration
      const siteConfigSuccess = await this.initializeSiteConfig();
      if (siteConfigSuccess) {
        console.log('Translation overlay system: Site-specific configuration loaded');
      } else {
        console.log('Translation overlay system: Using built-in configuration');
      }

      // Initialize core components
      this.initIntersectionObserver();
      this.setupScrollHandler();

      console.log('Translation overlay system initialized successfully');
      return true;
    } catch (error) {
      console.error('Translation overlay system initialization failed:', error);

      // Fallback initialization without site config
      this.initIntersectionObserver();
      this.setupScrollHandler();
      console.log('Translation overlay system initialized with fallback configuration');
      return false;
    }
  },

  // Clear cache for specific image
  clearImageCache: function(imageUrl) {
    this.imageCache.delete(imageUrl);
    console.log('Cache cleared for:', imageUrl);
  },

  // Clear all cache
  clearAllCache: function() {
    this.imageCache.clear();
    console.log('All cache cleared');
  },

  // Check if image is cached
  isImageCached: function(imageUrl) {
    return this.imageCache.has(imageUrl);
  },

  // Get processing status
  isImageProcessing: function(imageUrl) {
    return this.processingImages.has(imageUrl);
  },

  // Check if image has translation overlays
  hasImageOverlays: function(imageUrl) {
    let hasOverlays = false;
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl === imageUrl) {
        hasOverlays = true;
      }
    });
    return hasOverlays;
  },

  // 移除指定图片的所有overlay（改进版，更彻底的清理）
  removeImageOverlays: function(imageUrl) {
    const overlaysToRemove = [];
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl === imageUrl) {
        overlaysToRemove.push(id);
      }
    });

    overlaysToRemove.forEach(id => {
      this.removeOverlay(id);
    });

    // 同时清理相关的加载指示器和缓存指示器
    this.hideLoadingIndicator(imageUrl);
    this.hideCacheIndicator(imageUrl);

    // 更新操作按钮状态
    if (overlaysToRemove.length > 0) {
      this.updateActionButtonState(imageUrl, 'ready');
    }

    console.log('已移除图片的', overlaysToRemove.length, '个overlay:', imageUrl);
    return overlaysToRemove.length > 0;
  },

  // 移除指定图片的操作按钮
  removeActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button && button.parentNode) {
      button.parentNode.removeChild(button);
      this.actionButtons.delete(imageUrl);
      console.log('已移除图片的操作按钮:', imageUrl);
      return true;
    }
    return false;
  },

  // Enable/disable translation mode
  setTranslationMode: function(enabled, buttonText) {
    this.translationModeEnabled = enabled;
    this.buttonText = buttonText || 'Translate this image';

    if (enabled) {
      console.log('Translation mode enabled - initializing observers and showing buttons');

      // Initialize IntersectionObserver if not already done
      this.initIntersectionObserver();

      // Show action buttons on all eligible images
      this.showAllActionButtons();

      // Initialize visibility monitoring for better coverage
      if (this.initVisibilityMonitoring) {
        this.initVisibilityMonitoring();
      }

    } else {
      console.log('Translation mode disabled - hiding buttons and cleaning up');
      this.hideAllActionButtons();
    }

    console.log('Translation mode:', enabled ? 'enabled' : 'disabled');
  },

  // Show action buttons on all eligible images
  showAllActionButtons: function() {
    console.log('showAllActionButtons: Scanning for eligible images...');
    const images = document.querySelectorAll('img');
    let visibleCount = 0;
    let totalEligible = 0;

    images.forEach(img => {
      if (this.isMainContentImage(img)) {
        totalEligible++;

        // Check if image is currently visible
        const isVisible = this.isImageVisuallyVisible(img);
        if (isVisible) {
          visibleCount++;
          this.showActionButton(img);
        }
      }
    });

    console.log(`showAllActionButtons: Found ${totalEligible} eligible images, ${visibleCount} currently visible`);
  },

  // Hide all action buttons
  hideAllActionButtons: function() {
    console.log('hideAllActionButtons: Starting to hide all action buttons...');
    let removedCount = 0;

    this.actionButtons.forEach((button, imageUrl) => {
      try {
        if (button && button.parentNode) {
          button.parentNode.removeChild(button);
          removedCount++;
          console.log(`hideAllActionButtons: Removed button for ${imageUrl}`);
        } else if (button) {
          console.warn(`hideAllActionButtons: Button for ${imageUrl} has no parent node`);
        }
      } catch (error) {
        console.error(`hideAllActionButtons: Error removing button for ${imageUrl}:`, error);
      }
    });

    this.actionButtons.clear();
    console.log(`hideAllActionButtons: Completed. Removed ${removedCount} action buttons, cleared actionButtons map`);

    // Additional cleanup: remove any orphaned action buttons by class
    try {
      const orphanedButtons = document.querySelectorAll('.translation-action-button');
      orphanedButtons.forEach(button => {
        if (button.parentNode) {
          button.parentNode.removeChild(button);
          console.log('hideAllActionButtons: Removed orphaned action button');
        }
      });
    } catch (error) {
      console.error('hideAllActionButtons: Error removing orphaned buttons:', error);
    }
  },

  // Show action button on specific image
  showActionButton: function(img) {
    const imageUrl = this.normalizeImageUrl(img.src);

    // If button already exists, just show it
    if (this.actionButtons.has(imageUrl)) {
      this.showExistingActionButton(imageUrl);
      return;
    }

    // Don't create new button if image is being processed
    if (this.processingImages.has(imageUrl)) {
      return;
    }

    const button = document.createElement('button');
    button.className = 'translation-action-button';
    button.innerHTML = '🌐'; // Globe icon
    button.title = this.buttonText || 'Translate this image';

    // Position button relative to image
    const updateButtonPosition = () => {
      const rect = img.getBoundingClientRect();
      const scrollX = window.scrollX;
      const scrollY = window.scrollY;

      button.style.position = 'absolute';
      button.style.left = (rect.right + scrollX - 44) + 'px'; // 44 = button width + margin
      button.style.top = (rect.top + scrollY + 8) + 'px';
      button.style.zIndex = '10001';
    };

    // Initial positioning
    updateButtonPosition();

    // Handle click with improved state management
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Prevent clicks on disabled buttons
      if (button.disabled) {
        console.log('Button click ignored - button is disabled for', imageUrl);
        return;
      }

      if (this.processingImages.has(imageUrl)) {
        console.log('Button click ignored - already processing', imageUrl);
        return; // Already processing
      }

      // Check if image already has overlays (toggle functionality)
      if (this.hasImageOverlays(imageUrl)) {
        // Remove existing overlays
        console.log('Toggling OFF: Removing overlays for', imageUrl);
        const removed = this.removeImageOverlays(imageUrl);
        if (removed) {
          // Update button state to ready
          this.updateActionButtonState(imageUrl, 'ready');
          console.log('Toggled OFF: Removed overlays for', imageUrl);
        }
      } else {
        // Create new translation
        console.log('Toggling ON: Starting translation for', imageUrl);

        // Mark as processing to prevent duplicate requests
        this.processingImages.add(imageUrl);

        // Update button state to processing
        this.updateActionButtonState(imageUrl, 'processing');

        // Notify Flutter
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
          window.flutter_inappwebview.callHandler('onTranslateImageRequested', imageUrl);
          console.log('Toggled ON: Requesting translation for', imageUrl);
        } else {
          // Fallback: reset state if Flutter handler not available
          console.error('Flutter handler not available, resetting button state');
          this.processingImages.delete(imageUrl);
          this.updateActionButtonState(imageUrl, 'error');
        }
      }
    });

    // Update position on scroll/resize
    const positionHandler = () => updateButtonPosition();
    window.addEventListener('scroll', positionHandler);
    window.addEventListener('resize', positionHandler);

    // Store cleanup function
    button._cleanup = () => {
      window.removeEventListener('scroll', positionHandler);
      window.removeEventListener('resize', positionHandler);
    };

    document.body.appendChild(button);
    this.actionButtons.set(imageUrl, button);

    console.log('Action button shown for:', imageUrl);
  },

  // Update action button state with improved synchronization
  updateActionButtonState: function(imageUrl, state, forceUpdate = false) {
    const button = this.actionButtons.get(imageUrl);
    if (!button) {
      console.warn('updateActionButtonState: Button not found for', imageUrl);
      return;
    }

    console.log(`updateActionButtonState: ${imageUrl} -> ${state} (force: ${forceUpdate})`);

    // Clear all state classes
    button.classList.remove('processing', 'completed', 'error');

    switch (state) {
      case 'processing':
        button.classList.add('processing');
        button.innerHTML = '⏳';
        button.title = 'Translating...';
        button.disabled = true; // Prevent multiple clicks
        console.log('Button state: processing for', imageUrl);
        break;

      case 'completed':
        // Immediately set to completed state
        button.classList.add('completed');
        button.innerHTML = '👁️'; // Eye icon for toggle view
        button.title = 'Click to toggle original/translated view';
        button.disabled = false;
        console.log('Button state: completed (toggle mode) for', imageUrl);

        // Optional: Verify overlays exist after a delay, but don't auto-reset
        if (!forceUpdate) {
          setTimeout(() => {
            const hasOverlays = this.hasImageOverlays(imageUrl);
            console.log(`Button state verification: ${imageUrl} has overlays: ${hasOverlays}`);

            if (!hasOverlays) {
              console.warn(`Warning: Button marked as completed but no overlays found for ${imageUrl}`);
              // Don't auto-reset, just log the warning
              // The user can manually retry if needed
            }
          }, 500); // Longer delay for verification only
        }
        break;

      case 'error':
        button.classList.add('error');
        button.innerHTML = '❌';
        button.title = 'Translation failed - click to retry';
        button.disabled = false;
        console.log('Button state: error for', imageUrl);
        // Auto-reset to ready state after 3 seconds
        setTimeout(() => {
          if (button.classList.contains('error')) {
            this.updateActionButtonState(imageUrl, 'ready');
          }
        }, 3000);
        break;

      case 'ready':
      default:
        button.innerHTML = '🌐';
        button.title = this.buttonText || 'Translate this image';
        button.disabled = false;
        console.log('Button state: ready for', imageUrl);
        break;
    }
  },

  // Hide action button for specific image (when image leaves viewport)
  hideActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button) {
      button.style.display = 'none';
      console.log('Hidden action button for image:', imageUrl);
    }
  },

  // Show action button for specific image (when image enters viewport)
  showExistingActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button) {
      button.style.display = 'block';
      console.log('Shown existing action button for image:', imageUrl);
      return true;
    }
    return false;
  },

  // Remove action button for specific image (permanent removal)
  removeActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button) {
      if (button._cleanup) {
        button._cleanup();
      }
      if (button.parentNode) {
        button.parentNode.removeChild(button);
      }
      this.actionButtons.delete(imageUrl);
      console.log('Removed action button for image:', imageUrl);
    }
  },

  // Show cache indicator for cached images
  showCacheIndicator: function(imageUrl) {
    // Find the image element
    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        break;
      }
    }

    if (!targetImage) {
      console.warn('Target image not found for cache indicator:', imageUrl);
      return;
    }

    // Check if cache indicator already exists
    const existingIndicator = document.querySelector('[data-cache-indicator="' + imageUrl + '"]');
    if (existingIndicator) {
      return; // Already exists
    }

    const indicator = document.createElement('div');
    indicator.className = 'translation-cache-indicator';
    indicator.setAttribute('data-cache-indicator', imageUrl);
    indicator.innerHTML = '💾'; // Disk icon
    indicator.title = 'Translation cached - click to clear cache';

    // Position in top-left corner of image
    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    indicator.style.position = 'absolute';
    indicator.style.left = (imageRect.left + scrollX + 8) + 'px';
    indicator.style.top = (imageRect.top + scrollY + 8) + 'px';
    indicator.style.zIndex = '10001';
    indicator.style.width = '24px';
    indicator.style.height = '24px';
    indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    indicator.style.color = 'white';
    indicator.style.borderRadius = '12px';
    indicator.style.display = 'flex';
    indicator.style.alignItems = 'center';
    indicator.style.justifyContent = 'center';
    indicator.style.fontSize = '12px';
    indicator.style.cursor = 'pointer';
    indicator.style.zIndex = '10001';
    indicator.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';

    // Handle click to clear cache
    indicator.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Notify Flutter to clear cache
      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('onClearImageCache', imageUrl);
      }
    });

    // Update position on scroll/resize
    const updateIndicatorPosition = () => {
      const currentRect = targetImage.getBoundingClientRect();
      const currentScrollX = window.scrollX;
      const currentScrollY = window.scrollY;

      indicator.style.left = (currentRect.left + currentScrollX + 8) + 'px';
      indicator.style.top = (currentRect.top + currentScrollY + 8) + 'px';
    };

    const positionHandler = () => updateIndicatorPosition();
    window.addEventListener('scroll', positionHandler);
    window.addEventListener('resize', positionHandler);

    // Store cleanup function
    indicator._cleanup = () => {
      window.removeEventListener('scroll', positionHandler);
      window.removeEventListener('resize', positionHandler);
    };

    document.body.appendChild(indicator);
    console.log('Cache indicator shown for:', imageUrl);
  },

  // Hide cache indicator for specific image
  hideCacheIndicator: function(imageUrl) {
    const indicator = document.querySelector('[data-cache-indicator="' + imageUrl + '"]');
    if (indicator) {
      if (indicator._cleanup) {
        indicator._cleanup();
      }
      indicator.remove();
      console.log('Cache indicator hidden for:', imageUrl);
    }
  },

  // Hide all cache indicators
  hideAllCacheIndicators: function() {
    const indicators = document.querySelectorAll('.translation-cache-indicator');
    indicators.forEach(indicator => {
      if (indicator._cleanup) {
        indicator._cleanup();
      }
      indicator.remove();
    });
    console.log('All cache indicators hidden');
  },

  // 调试方法：获取所有图片的详细信息
  debugAllImages: function() {
    const allImages = [];
    const images = document.querySelectorAll('img');

    console.log(`=== 调试所有图片 (总共 ${images.length} 张) ===`);

    images.forEach((img, index) => {
      const url = this.normalizeImageUrl(img.src);
      const rect = img.getBoundingClientRect();
      const isMainContent = this.isMainContentImage(img);
      const isVisuallyVisible = this.isImageVisuallyVisible(img);

      const imageInfo = {
        index: index,
        url: url,
        displaySize: {
          width: img.width || img.offsetWidth || 0,
          height: img.height || img.offsetHeight || 0
        },
        naturalSize: {
          width: img.naturalWidth || 0,
          height: img.naturalHeight || 0
        },
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          height: rect.height
        },
        isMainContent: isMainContent,
        isVisuallyVisible: isVisuallyVisible,
        alt: img.alt || '',
        className: img.className || ''
      };

      allImages.push(imageInfo);

      console.log(`图片 ${index + 1}:`, imageInfo);

      if (isMainContent && isVisuallyVisible) {
        console.log(`  ✅ 这张图片会被处理`);
      } else {
        console.log(`  ❌ 这张图片会被跳过 (主要内容: ${isMainContent}, 可见: ${isVisuallyVisible})`);
      }
    });

    const mainContentImages = allImages.filter(img => img.isMainContent);
    const visibleMainContentImages = allImages.filter(img => img.isMainContent && img.isVisuallyVisible);

    console.log(`=== 总结 ===`);
    console.log(`总图片数: ${allImages.length}`);
    console.log(`主要内容图片数: ${mainContentImages.length}`);
    console.log(`可见的主要内容图片数: ${visibleMainContentImages.length}`);
    console.log(`会被处理的图片:`, visibleMainContentImages.map(img => img.url));

    return {
      allImages: allImages,
      mainContentImages: mainContentImages,
      visibleMainContentImages: visibleMainContentImages
    };
  },

  // =============================================================================
  // COMPREHENSIVE CLEANUP METHODS
  // =============================================================================

  // Remove all loading indicators
  removeAllLoadingIndicators: function() {
    console.log('Removing all loading indicators...');
    let removedCount = 0;

    this.loadingOverlays.forEach((loadingData, imageUrl) => {
      if (loadingData.element && loadingData.element.parentNode) {
        loadingData.element.parentNode.removeChild(loadingData.element);
        removedCount++;
      }
    });

    this.loadingOverlays.clear();
    console.log(`Removed ${removedCount} loading indicators`);
    return removedCount;
  },

  // Remove all cache indicators
  removeAllCacheIndicators: function() {
    console.log('Removing all cache indicators...');
    let removedCount = 0;

    // Remove cache indicators by class name
    const cacheIndicators = document.querySelectorAll('.cache-indicator, .translation-cache-indicator');
    cacheIndicators.forEach(indicator => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
        removedCount++;
      }
    });

    // Clear any cache-related data structures if they exist
    if (this.cacheIndicators) {
      this.cacheIndicators.clear();
    }

    console.log(`Removed ${removedCount} cache indicators`);
    return removedCount;
  },

  // Comprehensive cleanup method for translation toggle off
  performComprehensiveCleanup: function() {
    console.log('Starting comprehensive translation cleanup...');

    try {
      // Remove all overlays
      this.removeAllOverlays();

      // Remove all action buttons
      this.hideAllActionButtons();

      // Remove all loading indicators
      this.removeAllLoadingIndicators();

      // Remove all cache indicators
      this.removeAllCacheIndicators();

      // Clear all data structures
      this.overlays.clear();
      this.loadingOverlays.clear();
      this.actionButtons.clear();
      this.imageCache.clear();
      this.processingImages.clear();

      // Remove scroll handlers
      this.removeScrollHandler();

      // Disable translation mode
      this.setTranslationMode(false);

      // Remove any translation-related DOM elements
      this.removeTranslationDOMElements();

      console.log('Comprehensive translation cleanup completed successfully');
      return true;
    } catch (error) {
      console.error('Error during comprehensive cleanup:', error);
      return false;
    }
  },

  // Remove any translation-related DOM elements
  removeTranslationDOMElements: function() {
    console.log('Removing translation-related DOM elements...');

    try {
      // Remove elements with translation-related classes
      const translationElements = document.querySelectorAll(
        '.translation-overlay, .translation-loading, .translation-action-button, ' +
        '.translation-cache-indicator, [class*="translation-"], [data-translation-id]'
      );

      translationElements.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });

      // Remove translation-related attributes
      const elementsWithTranslationAttrs = document.querySelectorAll(
        '[data-translation-processed], [data-translation-id], [data-translation-overlay]'
      );

      elementsWithTranslationAttrs.forEach(element => {
        element.removeAttribute('data-translation-processed');
        element.removeAttribute('data-translation-id');
        element.removeAttribute('data-translation-overlay');
      });

      console.log(`Removed ${translationElements.length} translation DOM elements and cleaned attributes`);
    } catch (error) {
      console.error('Error removing translation DOM elements:', error);
    }
  },

  // Clear processing state for a specific image (called from Dart)
  clearProcessingState: function(imageUrl) {
    console.log('Clearing processing state for:', imageUrl);
    this.processingImages.delete(imageUrl);

    // Also ensure button is not disabled
    const button = this.actionButtons.get(imageUrl);
    if (button) {
      button.disabled = false;
    }
  },

  // Force reset button state (for error recovery)
  resetButtonState: function(imageUrl) {
    console.log('Force resetting button state for:', imageUrl);

    // Clear processing state
    this.processingImages.delete(imageUrl);

    // Reset button to ready state
    this.updateActionButtonState(imageUrl, 'ready');
  }
};