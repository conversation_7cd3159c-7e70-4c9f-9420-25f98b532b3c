/**
 * Base interface/template for site-specific configurations
 * All site configurations should extend or implement this interface
 */

class SiteConfigBase {
  constructor() {
    this.siteName = 'base';
    this.urlPatterns = [];
    this.initialized = false;
  }

  /**
   * Initialize site-specific configuration
   * Called when the site is detected and loaded
   */
  initialize() {
    this.initialized = true;
    console.log(`Site configuration initialized: ${this.siteName}`);
  }

  /**
   * Check if an image is main content (should be translated)
   * This is the core filtering method that each site should implement
   * @param {HTMLImageElement} img - The image element to check
   * @returns {boolean} - True if the image should be processed for translation
   */
  isMainContentImage(img) {
    // Default implementation - basic filtering
    if (!img || !img.src) return false;

    // Skip data URLs
    if (img.src.startsWith('data:')) return false;

    // Basic size filtering
    const minSize = 200;
    if (img.naturalWidth < minSize || img.naturalHeight < minSize) return false;
    if (img.offsetWidth < minSize || img.offsetHeight < minSize) return false;

    return true;
  }

  /**
   * Get additional image filters specific to this site
   * @returns {Object} - Filter configuration object
   */
  getImageFilters() {
    return {
      minWidth: 200,
      minHeight: 200,
      maxAspectRatio: 10,
      minAspectRatio: 0.1,
      excludeClasses: ['icon', 'avatar', 'thumbnail'],
      excludeSelectors: ['.ad', '.advertisement', '.banner']
    };
  }

  /**
   * Check if an image should be processed for translation
   * Combines main content check with additional filters
   * @param {HTMLImageElement} img - The image element to check
   * @returns {boolean} - True if the image should be processed
   */
  shouldProcessImage(img) {
    if (!this.isMainContentImage(img)) return false;

    const filters = this.getImageFilters();
    
    // Apply additional filters
    if (filters.excludeClasses) {
      for (const className of filters.excludeClasses) {
        if (img.classList.contains(className)) return false;
      }
    }

    if (filters.excludeSelectors) {
      for (const selector of filters.excludeSelectors) {
        if (img.matches(selector)) return false;
      }
    }

    return true;
  }

  /**
   * Get site-specific viewport buffer for intersection observer
   * @returns {string} - Root margin for IntersectionObserver
   */
  getViewportBuffer() {
    return '50px';
  }

  /**
   * Get site-specific intersection threshold
   * @returns {number} - Threshold for IntersectionObserver
   */
  getIntersectionThreshold() {
    return 0.1;
  }

  /**
   * Handle site-specific dynamic content loading
   * Called when new content is detected via MutationObserver
   * @param {MutationRecord[]} mutations - Array of mutation records
   */
  handleDynamicContent(mutations) {
    // Default implementation - basic handling
    console.log(`Handling dynamic content for ${this.siteName}:`, mutations.length, 'mutations');
  }

  /**
   * Get site-specific selectors for dynamic content monitoring
   * @returns {Object} - Selectors configuration
   */
  getDynamicContentSelectors() {
    return {
      imageContainers: ['div', 'section', 'article'],
      imageSelectors: ['img'],
      excludeContainers: ['.ad', '.advertisement', '.sidebar']
    };
  }

  /**
   * Check if the current page supports infinite scroll
   * @returns {boolean} - True if infinite scroll is supported
   */
  supportsInfiniteScroll() {
    return false;
  }

  /**
   * Check if the current page supports pagination
   * @returns {boolean} - True if pagination is supported
   */
  supportsPagination() {
    return true;
  }

  /**
   * Get debounce delay for dynamic content loading
   * @returns {number} - Delay in milliseconds
   */
  getDynamicContentDebounceDelay() {
    return 300;
  }

  /**
   * Cleanup site-specific resources
   * Called when switching sites or shutting down
   */
  cleanup() {
    this.initialized = false;
    console.log(`Site configuration cleaned up: ${this.siteName}`);
  }

  /**
   * Get site-specific configuration for debugging
   * @returns {Object} - Debug information
   */
  getDebugInfo() {
    return {
      siteName: this.siteName,
      urlPatterns: this.urlPatterns,
      initialized: this.initialized,
      supportsInfiniteScroll: this.supportsInfiniteScroll(),
      supportsPagination: this.supportsPagination()
    };
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SiteConfigBase;
} else {
  window.SiteConfigBase = SiteConfigBase;
}
