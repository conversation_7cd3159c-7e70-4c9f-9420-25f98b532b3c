/**
 * MangaDex site-specific configuration
 * Handles image filtering and content detection for manga reading sites
 */

class MangaDexSiteConfig extends SiteConfigBase {
  constructor() {
    super();
    this.siteName = 'mangadex';
    this.urlPatterns = [
      /mangadex\.org/i
    ];
  }

  /**
   * Initialize MangaDex-specific configuration
   */
  initialize() {
    super.initialize();
    console.log('MangaDex site configuration initialized');
  }

  /**
   * Check if an image is main content for MangaDex
   * @param {HTMLImageElement} img - The image element to check
   * @returns {boolean} - True if the image should be processed for translation
   */
  isMainContentImage(img) {
    if (!img || !img.src) {
      return false;
    }

    const url = this.normalizeImageUrl(img.src);
    console.log('MangaDex: Checking image:', url);

    // Skip data URLs
    if (url.startsWith('data:')) {
      console.log('Skipping data URL:', url);
      return false;
    }

    // Skip very small images (likely icons or decorative elements)
    const minSize = 200;
    if (img.naturalWidth > 0 && img.naturalHeight > 0) {
      if (img.naturalWidth < minSize || img.naturalHeight < minSize) {
        console.log('Skipping small image:', url, `${img.naturalWidth}x${img.naturalHeight}`);
        return false;
      }
    }

    if (img.offsetWidth > 0 && img.offsetHeight > 0) {
      if (img.offsetWidth < minSize || img.offsetHeight < minSize) {
        console.log('Skipping small displayed image:', url, `${img.offsetWidth}x${img.offsetHeight}`);
        return false;
      }
    }

    if (this.isMangaDexBackgroundImage(img)) {
      console.log('Skipping MangaDex background image:', url);
      return false;
    }

    console.log('MangaDex: Image passed all filters:', url);
    return true;
  }

  /**
   * Normalize image URL for consistent processing
   * @param {string} url - Original image URL
   * @returns {string} - Normalized URL
   */
  normalizeImageUrl(url) {
    if (!url) return '';
    
    // Remove query parameters and fragments for blob URLs
    if (url.startsWith('blob:')) {
      return url.split('?')[0].split('#')[0];
    }
    
    return url;
  }

  /**
   * Check if an image is a background image specific to MangaDex
   * @param {HTMLImageElement} img - The image element to check
   * @returns {boolean} - True if the image is a background image
   */
  isMangaDexBackgroundImage(img) {
    const url = this.normalizeImageUrl(img.src);

    // Check CSS classes for background indicators
    const hasBackgroundClass = (
      img.classList.contains('background') ||
      img.classList.contains('bg-image') ||
      img.classList.contains('backdrop') ||
      img.classList.contains('wallpaper') ||
      img.className.includes('bg-')
    );

    if (hasBackgroundClass) {
      console.log('MangaDex background class detected:', img.className);
      return true;
    }

    // Check CSS styles for background indicators
    const computedStyle = window.getComputedStyle(img);
    const hasBackgroundStyle = (
      computedStyle.position === 'fixed' ||
      computedStyle.position === 'absolute' ||
      (computedStyle.zIndex !== 'auto' && parseInt(computedStyle.zIndex) < 0)
    );

    if (hasBackgroundStyle) {
      console.log('MangaDex background style detected:', {
        position: computedStyle.position,
        zIndex: computedStyle.zIndex
      });
      return true;
    }

    // Check image size characteristics
    const hasBackgroundSize = (
      // Very small decorative images
      (img.naturalWidth < 100 && img.naturalHeight < 100) ||
      // Images with extreme scaling (likely decorative or background)
      (img.naturalWidth > 0 && img.naturalHeight > 0 &&
       img.offsetWidth > 0 && img.offsetHeight > 0 && (
         (img.offsetWidth / img.naturalWidth < 0.1) ||  // Scaled down to less than 10%
         (img.offsetHeight / img.naturalHeight < 0.1) ||
         (img.offsetWidth / img.naturalWidth > 10) ||   // Scaled up more than 10x
         (img.offsetHeight / img.naturalHeight > 10)
       ))
    );

    if (hasBackgroundSize) {
      console.log('MangaDex background size detected:', {
        natural: `${img.naturalWidth}x${img.naturalHeight}`,
        display: `${img.offsetWidth}x${img.offsetHeight}`,
        scaleX: img.offsetWidth / img.naturalWidth,
        scaleY: img.offsetHeight / img.naturalHeight
      });
      return true;
    }

    // Check image position characteristics (only for very large images that cover viewport)
    // const rect = img.getBoundingClientRect();
    // const hasBackgroundPosition = (
    //   // Images that cover the entire viewport or larger (likely background)
    //   (rect.width >= window.innerWidth && rect.height >= window.innerHeight)
    // );

    // if (hasBackgroundPosition) {
    //   console.log('MangaDex background position detected:', {
    //     rect: { width: rect.width, height: rect.height },
    //     viewport: { width: window.innerWidth, height: window.innerHeight }
    //   });
    //   return true;
    // }

    return false;
  }

  /**
   * Get MangaDex-specific image filters
   * @returns {Object} - Filter configuration object
   */
  getImageFilters() {
    return {
      minWidth: 200,
      minHeight: 200,
      maxAspectRatio: 20,  // Allow very tall manga pages
      minAspectRatio: 0.05, // Allow very wide manga pages
      excludeClasses: [
        'icon', 'avatar', 'thumbnail', 'logo', 'banner',
        'background', 'bg-image', 'backdrop', 'wallpaper'
      ],
      excludeSelectors: [
        '.ad', '.advertisement', '.banner', '.sidebar',
        '.header', '.footer', '.navigation', '.menu'
      ]
    };
  }

  /**
   * Get viewport buffer for manga pages (larger buffer for better UX)
   * @returns {string} - Root margin for IntersectionObserver
   */
  getViewportBuffer() {
    return '100px'; // Larger buffer for manga pages
  }

  /**
   * Get intersection threshold for manga images
   * @returns {number} - Threshold for IntersectionObserver
   */
  getIntersectionThreshold() {
    return 0.05; // Lower threshold for large manga images
  }

  /**
   * Check if the current page supports infinite scroll
   * @returns {boolean} - True if infinite scroll is supported
   */
  supportsInfiniteScroll() {
    // MangaDex typically uses infinite scroll for manga reading
    return true;
  }

  /**
   * Check if the current page supports pagination
   * @returns {boolean} - True if pagination is supported
   */
  supportsPagination() {
    // MangaDex also supports pagination
    return true;
  }

  /**
   * Get debounce delay for dynamic content loading (shorter for manga sites)
   * @returns {number} - Delay in milliseconds
   */
  getDynamicContentDebounceDelay() {
    return 150; // Faster response for manga reading
  }

  /**
   * Handle MangaDex-specific dynamic content loading
   * @param {MutationRecord[]} mutations - Array of mutation records
   */
  handleDynamicContent(mutations) {
    console.log('MangaDex: Handling dynamic content loading:', mutations.length, 'mutations');
    
    let newImagesFound = 0;
    
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is an image
            if (node.tagName === 'IMG') {
              if (this.isMainContentImage(node)) {
                newImagesFound++;
                console.log('MangaDex: New manga image detected:', node.src);
              }
            }
            
            // Check for images within the added node
            const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
            images.forEach(img => {
              if (this.isMainContentImage(img)) {
                newImagesFound++;
                console.log('MangaDex: New manga image in container:', img.src);
              }
            });
          }
        });
      }
    });
    
    if (newImagesFound > 0) {
      console.log(`MangaDex: Found ${newImagesFound} new manga images via dynamic loading`);
    }
  }

  /**
   * Get MangaDex-specific selectors for dynamic content monitoring
   * @returns {Object} - Selectors configuration
   */
  getDynamicContentSelectors() {
    return {
      imageContainers: [
        '.manga-page', '.chapter-page', '.reader-page',
        '.page-container', '.image-container',
        'div', 'section', 'article'
      ],
      imageSelectors: ['img'],
      excludeContainers: [
        '.ad', '.advertisement', '.sidebar', '.header',
        '.footer', '.navigation', '.menu', '.comments'
      ]
    };
  }

  /**
   * Get MangaDex-specific debug information
   * @returns {Object} - Debug information
   */
  getDebugInfo() {
    const baseInfo = super.getDebugInfo();
    return {
      ...baseInfo,
      MangaDexSpecific: {
        viewportBuffer: this.getViewportBuffer(),
        intersectionThreshold: this.getIntersectionThreshold(),
        debounceDelay: this.getDynamicContentDebounceDelay(),
        imageFilters: this.getImageFilters()
      }
    };
  }
}

// Register the MangaDex configuration
if (typeof window !== 'undefined') {
  window.MangaDexSiteConfig = MangaDexSiteConfig;
  
  // Auto-register with site loader if available
  if (window.SiteLoader) {
    // This would be handled by the site loader's detection system
    console.log('MangaDex configuration available for loading');
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MangaDexSiteConfig;
}
