import 'package:flutter_test/flutter_test.dart';
import 'package:imtrans/services/local_ocr_service.dart';

/// 页面级语言检测功能演示
/// 展示新的页面级检测相比于逐块检测的优势
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Page-Level Language Detection Demo', () {
    test('演示页面级语言检测的优势', () {
      print('\n🎯 页面级语言检测功能演示');
      print('=' * 60);
      
      // 场景1: 短文本块的语言检测挑战
      print('\n📝 场景1: 短文本块语言检测的挑战');
      print('-' * 40);
      
      final shortTextBlocks = [
        const OcrTextElement(
          text: 'の',
          boundingBox: OcrBoundingBox(left: 10, top: 10, right: 30, bottom: 30),
          confidence: 0.9,
        ),
        const OcrTextElement(
          text: 'は',
          boundingBox: OcrBoundingBox(left: 40, top: 10, right: 60, bottom: 30),
          confidence: 0.8,
        ),
        const OcrTextElement(
          text: 'を',
          boundingBox: OcrBoundingBox(left: 70, top: 10, right: 90, bottom: 30),
          confidence: 0.95,
        ),
        const OcrTextElement(
          text: '日本語',
          boundingBox: OcrBoundingBox(left: 100, top: 10, right: 150, bottom: 30),
          confidence: 0.92,
        ),
      ];

      print('原始文本块:');
      for (int i = 0; i < shortTextBlocks.length; i++) {
        final block = shortTextBlocks[i];
        print('  块${i + 1}: "${block.text}" (${block.text.length}字符)');
      }

      print('\n❌ 逐块检测的问题:');
      print('  • 单个字符"の"、"は"、"を"很难准确识别语言');
      print('  • 每个块可能被识别为不同的语言');
      print('  • 导致翻译不一致，质量下降');

      // 页面级检测的解决方案
      final combinedText = shortTextBlocks
          .map((element) => element.text.trim())
          .where((text) => text.isNotEmpty)
          .join(' ');

      print('\n✅ 页面级检测的解决方案:');
      print('  合并文本: "$combinedText"');
      print('  文本长度: ${combinedText.length}字符');
      print('  • 提供更多上下文信息');
      print('  • 包含明确的日语特征');
      print('  • 能够准确识别为日语');
      print('  • 所有块使用统一的源语言进行翻译');

      // 场景2: 混合语言页面的处理
      print('\n📝 场景2: 混合语言页面的主要语言检测');
      print('-' * 40);

      final mixedLanguageBlocks = [
        const OcrTextElement(
          text: 'Welcome to our website',
          boundingBox: OcrBoundingBox(left: 10, top: 10, right: 200, bottom: 30),
          confidence: 0.9,
        ),
        const OcrTextElement(
          text: 'こんにちは',
          boundingBox: OcrBoundingBox(left: 10, top: 40, right: 100, bottom: 60),
          confidence: 0.8,
        ),
        const OcrTextElement(
          text: 'Please click here to continue',
          boundingBox: OcrBoundingBox(left: 10, top: 70, right: 250, bottom: 90),
          confidence: 0.95,
        ),
        const OcrTextElement(
          text: 'Thank you for visiting',
          boundingBox: OcrBoundingBox(left: 10, top: 100, right: 200, bottom: 120),
          confidence: 0.93,
        ),
      ];

      print('混合语言页面内容:');
      for (int i = 0; i < mixedLanguageBlocks.length; i++) {
        final block = mixedLanguageBlocks[i];
        final isEnglish = RegExp(r'^[a-zA-Z\s]+$').hasMatch(block.text);
        final isJapanese = RegExp(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]').hasMatch(block.text);
        String language = isEnglish ? '英语' : (isJapanese ? '日语' : '其他');
        print('  块${i + 1}: "${block.text}" ($language)');
      }

      final mixedCombinedText = mixedLanguageBlocks
          .map((element) => element.text.trim())
          .where((text) => text.isNotEmpty)
          .join(' ');

      final words = mixedCombinedText.split(' ');
      final englishWords = words.where((word) => 
        RegExp(r'^[a-zA-Z]+$').hasMatch(word)).length;
      final japaneseWords = words.where((word) => 
        RegExp(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]').hasMatch(word)).length;

      print('\n语言分析结果:');
      print('  英语词汇数量: $englishWords');
      print('  日语词汇数量: $japaneseWords');
      print('  主要语言: ${englishWords > japaneseWords ? "英语" : "日语"}');

      print('\n✅ 页面级检测优势:');
      print('  • 能够识别页面的主要语言（英语）');
      print('  • 为所有文本块提供一致的翻译');
      print('  • 避免因个别块的语言误判导致的翻译错误');

      // 场景3: 翻译一致性的重要性
      print('\n📝 场景3: 翻译一致性的重要性');
      print('-' * 40);

      final consistentBlocks = [
        const OcrTextElement(
          text: 'First sentence of the document',
          boundingBox: OcrBoundingBox(left: 10, top: 10, right: 250, bottom: 30),
          confidence: 0.9,
        ),
        const OcrTextElement(
          text: 'Second sentence continues the topic',
          boundingBox: OcrBoundingBox(left: 10, top: 40, right: 260, bottom: 60),
          confidence: 0.8,
        ),
        const OcrTextElement(
          text: 'Third sentence concludes the paragraph',
          boundingBox: OcrBoundingBox(left: 10, top: 70, right: 270, bottom: 90),
          confidence: 0.95,
        ),
      ];

      print('文档内容:');
      for (int i = 0; i < consistentBlocks.length; i++) {
        final block = consistentBlocks[i];
        print('  句子${i + 1}: "${block.text}"');
      }

      print('\n❌ 逐块检测可能的问题:');
      print('  • 第一句可能被识别为英语');
      print('  • 第二句可能被误识别为其他语言');
      print('  • 第三句又被识别为英语');
      print('  • 导致翻译风格不一致');

      print('\n✅ 页面级检测的解决方案:');
      print('  • 所有句子都使用相同的源语言（英语）');
      print('  • 确保翻译术语和风格的一致性');
      print('  • 提供更好的用户体验');

      // 总结
      print('\n🎉 页面级语言检测功能总结');
      print('=' * 60);
      print('✅ 主要优势:');
      print('  1. 🎯 更准确的语言识别 - 使用更多文本上下文');
      print('  2. 🔄 一致的翻译质量 - 统一的源-目标语言对');
      print('  3. 🛡️ 健壮的错误处理 - 失败时回退到逐块检测');
      print('  4. ⚡ 更好的性能 - 减少重复的语言检测调用');
      print('  5. 👥 改善用户体验 - 更连贯的翻译结果');

      print('\n🔧 技术实现:');
      print('  • TranslationWorkflowService._detectPageLanguage()');
      print('  • LocalTranslationService.translateOcrElementsWithPageLanguage()');
      print('  • 保持现有OCR和缓存功能不变');
      print('  • 向后兼容，失败时自动回退');

      print('\n🚀 使用场景:');
      print('  • 漫画和图书翻译');
      print('  • 网页内容翻译');
      print('  • 文档图片翻译');
      print('  • 任何包含多个文本块的图像');

      print('\n' + '=' * 60);
      print('🎊 页面级语言检测功能演示完成！');
    });
  });
}
