import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/util/ocr_preferences.dart';

void main() {
  group('OcrPreferences', () {
    late OcrPreferences ocrPreferences;

    setUp(() {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
      ocrPreferences = OcrPreferences.instance;
    });

    tearDown(() {
      // 重置单例状态
      ocrPreferences.resetForTesting();
    });

    group('单例模式测试', () {
      test('应该返回相同的实例', () {
        final instance1 = OcrPreferences.instance;
        final instance2 = OcrPreferences.instance;
        
        expect(instance1, same(instance2));
      });
    });

    group('初始化测试', () {
      test('初始状态应该未初始化', () {
        expect(ocrPreferences.initialized, false);
        expect(ocrPreferences.useServerOcr, false);
      });

      test('应该成功初始化并加载默认设置', () async {
        await ocrPreferences.initialize();
        
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, false); // 默认使用本地OCR
      });

      test('应该从SharedPreferences加载已保存的设置', () async {
        // 预设保存的偏好设置
        SharedPreferences.setMockInitialValues({
          'use_server_ocr': true,
        });
        
        await ocrPreferences.initialize();
        
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, true);
      });

      test('重复初始化应该不会重复执行', () async {
        await ocrPreferences.initialize();
        expect(ocrPreferences.initialized, true);
        
        final firstInitValue = ocrPreferences.useServerOcr;
        
        // 再次初始化
        await ocrPreferences.initialize();
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, equals(firstInitValue));
      });

      test('初始化出错时应该使用默认值', () async {
        // 模拟SharedPreferences错误
        // 注意：在实际测试中可能需要使用更复杂的模拟方法
        
        await ocrPreferences.initialize();
        
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, false); // 默认值
      });
    });

    group('设置管理测试', () {
      setUp(() async {
        await ocrPreferences.initialize();
      });

      test('应该能够设置使用服务器OCR', () async {
        expect(ocrPreferences.useServerOcr, false);
        
        await ocrPreferences.setUseServerOcr(true);
        
        expect(ocrPreferences.useServerOcr, true);
        
        // 验证设置已保存到SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), true);
      });

      test('应该能够设置使用本地OCR', () async {
        // 先设置为服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        
        // 再设置为本地OCR
        await ocrPreferences.setUseServerOcr(false);
        
        expect(ocrPreferences.useServerOcr, false);
        
        // 验证设置已保存到SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), false);
      });

      test('设置相同值时应该不执行保存操作', () async {
        expect(ocrPreferences.useServerOcr, false);
        
        // 设置相同的值
        await ocrPreferences.setUseServerOcr(false);
        
        expect(ocrPreferences.useServerOcr, false);
      });

      test('设置变更应该触发监听器通知', () async {
        bool notificationReceived = false;
        
        ocrPreferences.addListener(() {
          notificationReceived = true;
        });
        
        await ocrPreferences.setUseServerOcr(true);
        
        expect(notificationReceived, true);
      });

      test('设置相同值时不应该触发监听器通知', () async {
        bool notificationReceived = false;
        
        ocrPreferences.addListener(() {
          notificationReceived = true;
        });
        
        // 设置相同的值（默认为false）
        await ocrPreferences.setUseServerOcr(false);
        
        expect(notificationReceived, false);
      });
    });

    group('描述文本测试', () {
      setUp(() async {
        await ocrPreferences.initialize();
      });

      test('应该返回正确的本地OCR描述', () {
        ocrPreferences.setUseServerOcr(false);
        
        final description = ocrPreferences.getOcrTypeDescription();
        
        expect(description, equals('Local OCR'));
      });

      test('应该返回正确的服务器OCR描述', () async {
        await ocrPreferences.setUseServerOcr(true);
        
        final description = ocrPreferences.getOcrTypeDescription();
        
        expect(description, equals('Server OCR'));
      });
    });

    group('持久化测试', () {
      test('设置应该在重新初始化后保持', () async {
        // 第一次初始化并设置
        await ocrPreferences.initialize();
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        
        // 重置状态模拟应用重启
        ocrPreferences.resetForTesting();
        
        // 重新初始化
        await ocrPreferences.initialize();
        
        // 验证设置被正确恢复
        expect(ocrPreferences.useServerOcr, true);
      });

      test('默认设置应该在首次使用时正确保存', () async {
        await ocrPreferences.initialize();
        
        // 验证默认设置
        expect(ocrPreferences.useServerOcr, false);
        
        // 验证SharedPreferences中的值
        final prefs = await SharedPreferences.getInstance();
        // 首次初始化时，如果没有保存过设置，SharedPreferences中可能没有对应的键
        // 这是正常的，因为我们使用默认值
        final savedValue = prefs.getBool('use_server_ocr');
        if (savedValue != null) {
          expect(savedValue, false);
        }
      });
    });

    group('错误处理测试', () {
      test('SharedPreferences保存失败时应该正确处理', () async {
        await ocrPreferences.initialize();
        
        // 在实际测试中，可能需要模拟SharedPreferences保存失败的情况
        // 这里只验证方法调用不会抛出异常
        expect(
          () => ocrPreferences.setUseServerOcr(true),
          returnsNormally,
        );
      });
    });
  });
}
