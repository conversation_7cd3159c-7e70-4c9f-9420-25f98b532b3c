// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Image Translator';

  @override
  String get targetLanguage => 'Target Language';

  @override
  String get language => 'Language';

  @override
  String get displayLanguage => 'Display Language';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Delete';

  @override
  String get rename => 'Rename';

  @override
  String get loading => 'Loading..';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get retry => 'Retry';

  @override
  String get account => 'Account';

  @override
  String get feedback => 'Feedback';

  @override
  String get about => 'About';

  @override
  String get signOut => 'Sign Out';

  @override
  String get translate => 'Translate';

  @override
  String get translating => 'Translating';

  @override
  String get translated => 'Translated';

  @override
  String get failed => 'Operation failed';

  @override
  String get selectTargetLanguage => 'Default translate to';

  @override
  String get targetLanguageDescription =>
      'The selected language below will be used as the default target language for translation.';

  @override
  String get darkModeDescription =>
      'Choose your preferred theme mode for the app.';

  @override
  String get selectTheme => 'Select Theme';

  @override
  String get appLanguageSetting => 'Language';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get appDescription =>
      'High-quality Manga & Image Translation \nwith Seamless Text Overlay';

  @override
  String get feedbackHint => 'Your feedback helps us improve';

  @override
  String get emailHint => 'Email (optional)';

  @override
  String get send => 'Send';

  @override
  String get deleteData => 'Delete Data';

  @override
  String get deleteDataWarningTitle => 'Delete Data?';

  @override
  String get deleteDataWarningText =>
      'Your account and all data will be deleted. This action cannot be undone.';

  @override
  String get done => 'Done';

  @override
  String get deleteDataSuccess => 'Your data has been completely deleted.';

  @override
  String get signIn => 'Sign In';

  @override
  String get browserSettings => 'Browser Settings';

  @override
  String get adBlocking => 'Ad Blocking';

  @override
  String get adBlockingDescription => 'Block ads and trackers while browsing';

  @override
  String get adBlockingEnabled => 'Ad blocking enabled';

  @override
  String get adBlockingDisabled => 'Ad blocking disabled';

  @override
  String get ocrProcessing => 'OCR Processing';

  @override
  String get localOcr => 'Local OCR';

  @override
  String get serverOcr => 'Server OCR';

  @override
  String get ocrProcessingDescription =>
      'Choose between local device processing or server-based OCR for text recognition';

  @override
  String get enterUrl => 'Enter URL or search keywords';

  @override
  String get errorLoadingImage => 'Error Loading Image';

  @override
  String get selectImages => 'Please select at least one image';

  @override
  String get noValidImages => 'No valid images to download';

  @override
  String processingError(String error) {
    return 'Failed to process images: $error';
  }

  @override
  String get autoRenew => 'Automatically renew, cancel at any time';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get operationTakingLong => 'Operation is taking longer than expected';

  @override
  String get system => 'System';

  @override
  String get light => 'Light';

  @override
  String get dark => 'Dark';

  @override
  String get chinese => 'Chinese';

  @override
  String get english => 'English';

  @override
  String get japanese => 'Japanese';

  @override
  String get korean => 'Korean';

  @override
  String get french => 'French';

  @override
  String get german => 'German';

  @override
  String get spanish => 'Spanish';

  @override
  String get italian => 'Italian';

  @override
  String get thai => 'Thai';

  @override
  String get vietnamese => 'Vietnamese';

  @override
  String get indonesian => 'Indonesian';

  @override
  String get malay => 'Malay';

  @override
  String get feedbackSuccess => 'Thank you for your feedback!';

  @override
  String get feedbackError => 'Failed to submit feedback. Please try again.';

  @override
  String get feedbackEmpty => 'Sorry but the content is empty';

  @override
  String get feedbackSendError => 'Failed to send feedback';

  @override
  String get generalError => 'An error occurred. Please try again.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get deleteAccountWarning =>
      'This action cannot be undone. All your data will be permanently deleted.';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get deleteSuccess => 'Account deleted successfully';

  @override
  String get deleteError => 'Failed to delete account. Please try again.';

  @override
  String get subscriptionDescription =>
      'Automatically renew, cancel at any time';

  @override
  String get subscribe => 'Subscribe';

  @override
  String get restore => 'Restore';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get monthly => 'Monthly';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get bestValue => 'Best Value';

  @override
  String get unlimitedManga => 'Unlimited manga generation';

  @override
  String get highQuality => 'High quality images';

  @override
  String get prioritySupport => 'Priority support';

  @override
  String get noAds => 'No ads';

  @override
  String get popular => 'Popular';

  @override
  String get freeTrial => 'Free Trial';

  @override
  String freeTrialDescription(String price) {
    return 'Enjoy 3-day free trial, then $price weekly';
  }

  @override
  String get fileUploadFeature => 'Supports File Upload For Translation';

  @override
  String get higherResolutionFeature => 'Higher Resolution';

  @override
  String get accurateTranslationFeature => 'More Accurate Translation';

  @override
  String get unlimitedTranslationsFeature =>
      'Unlimited Daily Image Translations';

  @override
  String get adFreeFeature => 'Ad-Free Experience';

  @override
  String get accountError => 'Account error';

  @override
  String get noProductsError => 'No products available';

  @override
  String get processing => 'Processing...';

  @override
  String get purchaseFailed => 'Purchase failed';

  @override
  String get restoring => 'Restoring purchases...';

  @override
  String get tryAgainLater => 'Please try again later';

  @override
  String get networkError => 'Network error';

  @override
  String get importFile => 'Import File';

  @override
  String get album => 'Album';

  @override
  String get camera => 'Camera';

  @override
  String get translateWebImages => 'Translate Web Images';

  @override
  String get recentTranslations => 'Recent Translations';

  @override
  String get seeAll => 'See All';

  @override
  String get noTranslationHistory => 'No translations yet';

  @override
  String get allFiles => 'All Files';

  @override
  String get viewMode => 'View Mode';

  @override
  String get listMode => 'List Mode';

  @override
  String get smallGridMode => 'Small Grid Mode';

  @override
  String get largeGridMode => 'Large Grid Mode';

  @override
  String get listModeDescription => 'Compact list display';

  @override
  String get smallGridModeDescription => 'Small size grid display';

  @override
  String get largeGridModeDescription => 'Large size waterfall display';

  @override
  String get singleImage => 'Single image';

  @override
  String multipleImages(int count) {
    return '$count images';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return 'Selected $count';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return 'Selected $count/$total';
  }

  @override
  String get noFilesFound => 'No files found';

  @override
  String get download => 'Download';

  @override
  String get downloading => 'Downloading..';

  @override
  String get imageSavedToGallery => 'Image saved to gallery';

  @override
  String get failedToSaveImage => 'Failed to save image';

  @override
  String get noImagesToSave => 'No images to save';

  @override
  String get imageIndexOutOfRange => 'Image index out of range';

  @override
  String get noTranslationResultSavingOriginal =>
      'No translation result, saving original image';

  @override
  String get imageContentNotFound => 'Cannot get image content';

  @override
  String get imageDataGenerationFailed => 'Image data generation failed';

  @override
  String get imageSavedSuccessfully => 'Image saved successfully';

  @override
  String get savingFailed => 'Saving failed';

  @override
  String get originalImageSavedSuccessfully =>
      'Original image saved successfully';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Network request failed: $statusCode';
  }

  @override
  String get cannotGetImageData => 'Cannot get image data';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Failed to save original image: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'Saving translated image requires Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'Saving translated images requires RepaintBoundary implementation, please use saveCurrentDisplayImage method';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Failed to save translated image: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count images saved successfully';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount images saved successfully, $failureCount failed';
  }

  @override
  String errorDownloading(String error) {
    return 'Error downloading: $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Loading failed $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return 'Delete $count item$s?';
  }

  @override
  String get drafts => 'Pending Tasks';

  @override
  String toLanguage(String lang) {
    return 'To: $lang';
  }

  @override
  String get translateAll => 'Translate All';

  @override
  String get nothingSelected => 'Nothing selected';

  @override
  String operationFailed(String error) {
    return 'Failed: $error';
  }

  @override
  String get allJobsDone => 'All Jobs Done';

  @override
  String get imageNotFound => 'Image not found';

  @override
  String usesLeftToday(int count) {
    return '$count Uses left today';
  }

  @override
  String get upgradeToPro => 'Upgrade to PRO for unlimited access';

  @override
  String get upgradeNow => 'GET PRO';

  @override
  String get updatingTranslationPosition => 'Updating translation position...';

  @override
  String get noImagesFound => 'No images found or lazy loading in progress';

  @override
  String get localTranslationInitializing =>
      'Initializing local translation services...';

  @override
  String get localTranslationFailed => 'Local translation failed';

  @override
  String get ocrServiceNotInitialized => 'OCR service not initialized';

  @override
  String get translationServiceNotInitialized =>
      'Translation service not initialized';

  @override
  String get processingImagesForTranslation =>
      'Processing images for translation...';

  @override
  String localTranslationCompleted(int count) {
    return 'Local translation completed for $count images';
  }

  @override
  String get newTab => 'New Tab';

  @override
  String get addBookmark => 'Add Bookmark';

  @override
  String get removeBookmark => 'Remove Bookmark';

  @override
  String get unableToProcessImages =>
      'Unable to find any images to process on the page.';

  @override
  String downloadFailed(String error) {
    return 'Download failed: $error';
  }

  @override
  String get selectAtLeastOneImage => 'Please select at least one image';

  @override
  String get noValidImagesToDownload => 'No valid images to download';

  @override
  String failedToProcessImages(String error) {
    return 'Failed to process images: $error';
  }

  @override
  String get loadingImages => 'Loading images...';

  @override
  String get deleteThisItem => 'Delete this item?';

  @override
  String get errorLoadingImageViewer => 'Error loading image viewer';

  @override
  String get failedToDeleteImage => 'Failed to delete image';

  @override
  String completedTranslationAt(String time) {
    return 'Complete translation at \n$time';
  }

  @override
  String get dailyLimitReached => 'Daily Limit Reached';

  @override
  String get quotaResetMessage =>
      'Free quota will reset tomorrow, Watch ads for more';

  @override
  String get upgradeButton => 'Upgrade';

  @override
  String get tryTomorrowButton => 'Try Tomorrow';

  @override
  String get followSystem => 'Follow System';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Unlock file translation';

  @override
  String get highQualityTranslation => 'High quality translation';

  @override
  String get adFreeExperience => 'Ad-free experience';

  @override
  String get getPro => 'GET PRO';

  @override
  String get loadFailed => 'Load Failed, please check your network connection';

  @override
  String get back => 'Back';

  @override
  String get purchaseSuccessful => 'Purchase successful';

  @override
  String get purchaseRestored => 'Purchase restored';

  @override
  String restoreFailed(String error) {
    return 'Restore failed: $error';
  }

  @override
  String get weekly => 'Weekly';

  @override
  String get annual => 'Annual';

  @override
  String get free => 'FREE';

  @override
  String get freeText => '3 Days Free Trial';

  @override
  String get billedMonthly => 'Billed Monthly';

  @override
  String get billedAnnual => 'Billed Annual';

  @override
  String get billedWeekly => 'Billed Weekly';

  @override
  String get freeTrialText => '3 Days Free Trial';

  @override
  String get save30Percent => 'Save 30%';

  @override
  String get loginTitle => 'Login to your\n Account';

  @override
  String get watchAd => 'Get More';

  @override
  String get watchAdDescription => 'Watch an ad to get more usage quota';

  @override
  String get fontSettings => 'Font Settings';

  @override
  String get fontFamily => 'Font Family';

  @override
  String get strokeSettings => 'Stroke Settings';

  @override
  String get textStyleSettings => 'Text Style Settings';

  @override
  String get resetToDefaults => 'Reset to Defaults';

  @override
  String get fontSize => 'Font Size';

  @override
  String get fontWeight => 'Font Weight';

  @override
  String get textColor => 'Text Color';

  @override
  String get textLabel => 'Text';

  @override
  String get strokeLabel => 'Stroke';

  @override
  String get strokeWidth => 'Stroke Width';

  @override
  String get shadowEffect => 'Shadow Effect';

  @override
  String get opacity => 'Opacity';

  @override
  String get horizontalOffset => 'Horizontal Offset';

  @override
  String get verticalOffset => 'Vertical Offset';

  @override
  String get blurRadius => 'Blur Radius';

  @override
  String get fontWeightThin => 'Thin';

  @override
  String get fontWeightNormal => 'Normal';

  @override
  String get fontWeightMedium => 'Medium';

  @override
  String get fontWeightSemiBold => 'Semi Bold';

  @override
  String get fontWeightBold => 'Bold';

  @override
  String get fontWeightExtraBold => 'Extra Bold';

  @override
  String get fontWeightBlack => 'Black';

  @override
  String get recommendedWebsites => 'Recommended Websites';

  @override
  String get myBookmarks => 'Bookmarks';

  @override
  String get noBookmarks => 'No bookmarks yet';

  @override
  String get bookmarkAdded => 'Bookmark added';

  @override
  String get alreadyBookmarked => 'Already bookmarked';

  @override
  String get cannotBookmarkEmptyPage => 'Cannot bookmark empty page';

  @override
  String get bookmarkFailed => 'Failed to add bookmark';

  @override
  String get deleteBookmark => 'Delete Bookmark';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'Are you sure you want to delete bookmark \"$title\"?';
  }

  @override
  String get bookmarkDeleted => 'Bookmark deleted';

  @override
  String get manageBookmarks => 'Manage';

  @override
  String get allBookmarks => 'All Bookmarks';

  @override
  String get browsingHistory => 'History';

  @override
  String get noBrowsingHistory => 'No browsing history yet';

  @override
  String get deleteHistory => 'Delete History';

  @override
  String get deleteHistoryConfirm =>
      'Are you sure you want to delete this history item?';

  @override
  String get historyDeleted => 'History item deleted';

  @override
  String get justNow => 'Just now';

  @override
  String minutesAgo(int count) {
    return '$count minutes ago';
  }

  @override
  String hoursAgo(int count) {
    return '$count hours ago';
  }

  @override
  String daysAgo(int count) {
    return '$count days ago';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return 'Downloading language files ($languageName)';
  }

  @override
  String get translationInProgress => 'Translation in progress...';

  @override
  String get translateThisImage => 'Translate this image';

  @override
  String get imageTranslationLoading => 'Translating image...';

  @override
  String get downloadTimeout =>
      'Download timeout, please check your network connection';
}
