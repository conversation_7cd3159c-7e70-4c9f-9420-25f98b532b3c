import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// WebView JavaScript 资源管理类
/// 支持多种 JS 文件和配置的通用资源管理系统
class WebViewJsResources {
  // 资源文件路径配置
  static final Map<String, String> _resourcePaths = {
    // 站点配置系统（必须在翻译覆盖层之前加载）
    'site_base': 'assets/js/sites/site_base.js',
    'site_loader': 'assets/js/sites/site_loader.js',
    'mangadex_config': 'assets/js/sites/mangadex.js',

    // 翻译覆盖层相关
    'translation_overlay': 'assets/js/translation_overlay.js',
    'translation_overlay_styles': 'assets/js/translation_overlay_styles.css',

    // 浏览器功能相关
    'browser_utils': 'assets/js/browser_utils.js',
    // 'ad_block': 'assets/js/ad_block.js',
    'content_extractor': 'assets/js/content_extractor.js',

    // 页面增强功能
    // 'page_enhancer': 'assets/js/page_enhancer.js',
    // 'scroll_handler': 'assets/js/scroll_handler.js',

    // 用户交互功能
    // 'gesture_handler': 'assets/js/gesture_handler.js',
    // 'context_menu': 'assets/js/context_menu.js',

    // 性能优化
    // 'lazy_loading': 'assets/js/lazy_loading.js',
    // 'image_optimizer': 'assets/js/image_optimizer.js',
  };

  // 缓存存储
  static final Map<String, String> _contentCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  
  // 缓存配置
  static const Duration _cacheExpiration = Duration(hours: 1);
  static const int _maxCacheSize = 50; // 最大缓存文件数

  /// 获取 JavaScript 代码内容
  /// [resourceName] 资源名称，对应 _resourcePaths 中的 key
  static Future<String> getJsContent(String resourceName) async {
    final path = _resourcePaths[resourceName];
    if (path == null) {
      throw Exception('Resource not found: $resourceName');
    }
    
    return await _getContent(path, resourceName);
  }
  
  /// 获取 CSS 样式内容
  static Future<String> getCssContent(String resourceName) async {
    final path = _resourcePaths[resourceName];
    if (path == null) {
      throw Exception('Resource not found: $resourceName');
    }
    
    return await _getContent(path, resourceName);
  }

  /// 获取任意资源内容
  static Future<String> getContent(String resourceName) async {
    final path = _resourcePaths[resourceName];
    if (path == null) {
      throw Exception('Resource not found: $resourceName');
    }
    
    return await _getContent(path, resourceName);
  }

  /// 内部获取内容的统一方法
  static Future<String> _getContent(String path, String resourceName) async {
    // 检查缓存
    if (_contentCache.containsKey(resourceName)) {
      final timestamp = _cacheTimestamps[resourceName];
      if (timestamp != null && DateTime.now().difference(timestamp) < _cacheExpiration) {
        return _contentCache[resourceName]!;
      } else {
        // 缓存过期，移除
        _contentCache.remove(resourceName);
        _cacheTimestamps.remove(resourceName);
      }
    }
    
    try {
      final content = await rootBundle.loadString(path);
      
      // 添加到缓存
      _addToCache(resourceName, content);
      
      return content;
    } catch (e) {
      throw Exception('Failed to load resource $resourceName from $path: $e');
    }
  }

  /// 添加内容到缓存
  static void _addToCache(String resourceName, String content) {
    // 检查缓存大小限制
    if (_contentCache.length >= _maxCacheSize) {
      // 移除最旧的缓存项
      String? oldestKey;
      DateTime? oldestTime;
      
      for (final entry in _cacheTimestamps.entries) {
        if (oldestTime == null || entry.value.isBefore(oldestTime)) {
          oldestTime = entry.value;
          oldestKey = entry.key;
        }
      }
      
      if (oldestKey != null) {
        _contentCache.remove(oldestKey);
        _cacheTimestamps.remove(oldestKey);
      }
    }
    
    _contentCache[resourceName] = content;
    _cacheTimestamps[resourceName] = DateTime.now();
  }

  /// 预加载多个资源
  static Future<void> preloadResources(List<String> resourceNames) async {
    final futures = resourceNames.map((name) => getContent(name));
    await Future.wait(futures);
  }

  /// 预加载所有资源
  static Future<void> preloadAllResources() async {
    final futures = _resourcePaths.keys.map((name) => getContent(name));
    await Future.wait(futures);
  }

  /// 检查资源是否存在
  static bool hasResource(String resourceName) {
    return _resourcePaths.containsKey(resourceName);
  }

  /// 获取所有可用的资源名称
  static List<String> getAvailableResources() {
    return _resourcePaths.keys.toList();
  }

  /// 获取资源路径
  static String? getResourcePath(String resourceName) {
    return _resourcePaths[resourceName];
  }

  /// 清除指定资源的缓存
  static void clearCache(String resourceName) {
    _contentCache.remove(resourceName);
    _cacheTimestamps.remove(resourceName);
  }

  /// 清除所有缓存
  static void clearAllCache() {
    _contentCache.clear();
    _cacheTimestamps.clear();
  }

  /// 获取缓存统计信息
  static Map<String, dynamic> getCacheStats() {
    return {
      'cachedResources': _contentCache.length,
      'maxCacheSize': _maxCacheSize,
      'cacheExpiration': _cacheExpiration.inMinutes,
      'cachedItems': _contentCache.keys.toList(),
    };
  }

  /// 检查缓存状态
  static bool isCached(String resourceName) {
    if (!_contentCache.containsKey(resourceName)) return false;
    
    final timestamp = _cacheTimestamps[resourceName];
    return timestamp != null && DateTime.now().difference(timestamp) < _cacheExpiration;
  }

  /// 获取缓存大小（字节）
  static int getCacheSize() {
    int totalSize = 0;
    for (final content in _contentCache.values) {
      totalSize += content.length;
    }
    return totalSize;
  }

  /// 添加自定义资源路径
  static void addResourcePath(String resourceName, String path) {
    _resourcePaths[resourceName] = path;
  }

  /// 移除资源路径
  static void removeResourcePath(String resourceName) {
    _resourcePaths.remove(resourceName);
    clearCache(resourceName);
  }

  /// 批量注入多个 JavaScript 资源
  /// 返回注入结果的映射
  static Future<Map<String, bool>> injectMultipleResources(
    List<String> resourceNames,
    Future<void> Function(String script) injectFunction,
  ) async {
    final results = <String, bool>{};
    
    for (final resourceName in resourceNames) {
      try {
        final content = await getContent(resourceName);
        await injectFunction(content);
        results[resourceName] = true;
      } catch (e) {
        results[resourceName] = false;
      }
    }
    
    return results;
  }

  /// 获取资源依赖关系（用于按正确顺序加载）
  static List<String> getResourceDependencies(String resourceName) {
    // 定义资源依赖关系
    const dependencies = {
      // 站点配置系统依赖关系
      'site_loader': ['site_base'],
      'mangadex_config': ['site_base'],

      // 翻译覆盖层需要站点配置系统
      'translation_overlay': ['browser_utils', 'site_base', 'site_loader', 'mangadex_config'],

      // 其他资源依赖关系
      'ad_block': ['browser_utils'],
      'content_extractor': ['browser_utils'],
      // 'page_enhancer': ['browser_utils', 'scroll_handler'],
      // 'gesture_handler': ['browser_utils'],
      // 'context_menu': ['browser_utils'],
      // 'lazy_loading': ['browser_utils'],
      // 'image_optimizer': ['browser_utils', 'lazy_loading'],
    };

    return dependencies[resourceName] ?? [];
  }

  /// 按依赖顺序加载资源
  static Future<List<String>> loadResourcesWithDependencies(
    List<String> resourceNames,
    Future<void> Function(String script) injectFunction,
  ) async {
    final loadedResources = <String>[];
    final toLoad = <String>{};

    // 递归添加所有依赖项
    void addResourceAndDependencies(String resourceName) {
      if (toLoad.contains(resourceName)) return;

      toLoad.add(resourceName);
      final dependencies = getResourceDependencies(resourceName);
      for (final dep in dependencies) {
        addResourceAndDependencies(dep);
      }
    }

    // 添加所有请求的资源及其依赖
    for (final resourceName in resourceNames) {
      addResourceAndDependencies(resourceName);
    }

    debugPrint('WebViewJsResources: Loading resources in dependency order: ${toLoad.toList()}');

    while (toLoad.isNotEmpty) {
      final canLoadNow = <String>{};

      for (final resource in toLoad) {
        final dependencies = getResourceDependencies(resource);
        final allDependenciesLoaded = dependencies.every((dep) => loadedResources.contains(dep));

        if (allDependenciesLoaded) {
          canLoadNow.add(resource);
        }
      }

      if (canLoadNow.isEmpty) {
        // 检测循环依赖
        throw Exception('Circular dependency detected in resources: ${toLoad.join(', ')}');
      }

      // 加载可以加载的资源
      for (final resource in canLoadNow) {
        try {
          debugPrint('WebViewJsResources: Loading resource: $resource');
          final content = await getContent(resource);
          await injectFunction(content);
          loadedResources.add(resource);
          toLoad.remove(resource);
          debugPrint('WebViewJsResources: Successfully loaded: $resource');
        } catch (e) {
          // 记录错误但继续加载其他资源
          debugPrint('Failed to load resource $resource: $e');
        }
      }
    }
    
    return loadedResources;
  }
} 