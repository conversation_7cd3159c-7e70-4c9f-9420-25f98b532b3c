import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:imtrans/services/account.dart';

/// AppsFlyer Attribution and Analytics Service
/// 
/// Provides mobile app attribution tracking, deep linking, and analytics
/// Integrates with existing Firebase Analytics for comprehensive tracking
class AppsFlyerService {
  static AppsflyerSdk? _appsflyerSdk;
  static bool _isInitialized = false;
  static bool _isDebugMode = kDebugMode;
  
  // Configuration constants - Replace with your actual AppsFlyer credentials
  static const String _devKey = 'm8RpWhxetPzmsCdGy4aMh6'; 
  static const String _appId = '**********'; // iOS App ID (numeric only, without 'id' prefix)
  
  /// Initialize AppsFlyer SDK
  /// 
  /// Call this during app startup after Firebase initialization
  static Future<void> initialize({
    String? devKey,
    String? appId,
    bool? debugMode,
  }) async {
    if (_isInitialized) {
      debugPrint('AppsFlyer already initialized');
      return;
    }
    
    try {
      final String finalDevKey = devKey ?? _devKey;
      final String finalAppId = appId ?? _appId;
      final bool finalDebugMode = debugMode ?? _isDebugMode;
      
      if (finalDevKey == 'YOUR_APPSFLYER_DEV_KEY') {
        debugPrint('⚠️ AppsFlyer: Please configure your dev key');
        return;
      }
      
      // Initialize AppsFlyer SDK
      final AppsFlyerOptions options = AppsFlyerOptions(
        afDevKey: finalDevKey,
        appId: finalAppId,
        showDebug: finalDebugMode,
        timeToWaitForATTUserAuthorization: 60, // iOS 14.5+ ATT timeout
        disableAdvertisingIdentifier: false,
        disableCollectASA: false, // Apple Search Ads attribution
      );
      
      _appsflyerSdk = AppsflyerSdk(options);
      
      // Set up conversion data callback
      _appsflyerSdk!.onInstallConversionData((data) {
        debugPrint('🔍 AppsFlyer Install Conversion Data Received: $data');
        debugPrint('🔍 conversions.appsflyersdk - Install UID: ${data['af_status']}');
        _handleInstallConversionData(data);
      });
      
      // Set up app open attribution callback
      _appsflyerSdk!.onAppOpenAttribution((data) {
        debugPrint('AppsFlyer App Open Attribution: $data');
        _handleAppOpenAttribution(data);
      });
      
      // Set up deep linking callback
      _appsflyerSdk!.onDeepLinking((result) {
        debugPrint('AppsFlyer Deep Link: $result');
        _handleDeepLink(result);
      });
      
      // Initialize the SDK
      debugPrint('🚀 AppsFlyer: Starting SDK initialization...');
      await _appsflyerSdk!.initSdk();
      debugPrint('🚀 AppsFlyer: SDK initialization completed');

      // Get and log AppsFlyer UID immediately after initialization
      try {
        final uid = await _appsflyerSdk!.getAppsFlyerUID();
        debugPrint('🔍 conversions.appsflyersdk - AppsFlyer UID: $uid');
      } catch (e) {
        debugPrint('⚠️ Failed to get AppsFlyer UID: $e');
      }

      // Set user ID from account service
      try {
        final account = await Account.instance;
        final uid = account.info['uid']?.toString();
        if (uid != null && uid.isNotEmpty) {
          setCustomerUserId(uid);
        }
      } catch (e) {
        debugPrint('AppsFlyer: Could not set user ID: $e');
      }
      
      _isInitialized = true;
      debugPrint('✅ AppsFlyer initialized successfully');
      
      // Track app launch
      await logEvent('app_launch', {});
      
    } catch (e) {
      debugPrint('❌ AppsFlyer initialization failed: $e');
    }
  }
  
  /// Set customer user ID for cross-device tracking
  static Future<void> setCustomerUserId(String userId) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('AppsFlyer not initialized');
      return;
    }
    
    try {
      _appsflyerSdk!.setCustomerUserId(userId);
      debugPrint('AppsFlyer: Set customer user ID: $userId');
    } catch (e) {
      debugPrint('AppsFlyer: Failed to set customer user ID: $e');
    }
  }
  
  /// Log custom event with parameters
  static Future<void> logEvent(String eventName, Map<String, dynamic> parameters) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('AppsFlyer not initialized, skipping event: $eventName');
      return;
    }
    
    try {
      // Convert parameters to string values as required by AppsFlyer
      final Map<String, dynamic> convertedParams = {};
      parameters.forEach((key, value) {
        convertedParams[key] = value.toString();
      });
      
      await _appsflyerSdk!.logEvent(eventName, convertedParams);
      debugPrint('AppsFlyer: Logged event: $eventName with params: $convertedParams');
    } catch (e) {
      debugPrint('AppsFlyer: Failed to log event $eventName: $e');
    }
  }
  
  /// Track user registration
  static Future<void> logRegistration(String method) async {
    await logEvent('af_complete_registration', {
      'af_registration_method': method,
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track user login
  static Future<void> logLogin(String method) async {
    await logEvent('af_login', {
      'af_login_method': method,
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track purchase events
  static Future<void> logPurchase({
    required double revenue,
    required String currency,
    String? productId,
    String? productName,
    String? category,
  }) async {
    await logEvent('af_purchase', {
      'af_revenue': revenue.toString(),
      'af_currency': currency,
      'af_content_id': productId ?? '',
      'af_content_type': category ?? 'subscription',
      'af_content': productName ?? '',
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }

  /// 专门处理购买成功的AppsFlyer事件上报
  ///
  /// 该方法由IAP购买服务在验证成功后调用，确保购买事件正确上报到AppsFlyer
  ///
  /// 参数：
  /// - [revenue]: 购买金额
  /// - [currency]: 货币代码（如USD、EUR等）
  /// - [productId]: 产品ID
  /// - [productName]: 产品名称
  /// - [category]: 产品类别，默认为'subscription'
  static Future<void> logPurchaseSuccess({
    required double revenue,
    required String currency,
    required String productId,
    required String productName,
    String? category,
  }) async {
    try {
      debugPrint('🛒 AppsFlyer: 记录购买成功事件');
      debugPrint('🛒 产品: $productName ($productId)');
      debugPrint('🛒 金额: $revenue $currency');

      await logEvent('af_purchase', {
        'af_revenue': revenue.toString(),
        'af_currency': currency,
        'af_content_id': productId,
        'af_content_type': category ?? 'subscription',
        'af_content': productName,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      debugPrint('✅ AppsFlyer: 购买事件上报成功');
    } catch (e) {
      debugPrint('❌ AppsFlyer: 购买事件上报失败: $e');
      // 不抛出异常，避免影响购买流程
    }
  }
  
  /// Track content view
  static Future<void> logContentView({
    required String contentType,
    required String contentId,
    String? contentName,
  }) async {
    await logEvent('af_content_view', {
      'af_content_type': contentType,
      'af_content_id': contentId,
      'af_content': contentName ?? '',
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track search events
  static Future<void> logSearch(String searchTerm) async {
    await logEvent('af_search', {
      'af_search_string': searchTerm,
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track share events
  static Future<void> logShare({
    required String contentType,
    required String contentId,
    String? method,
  }) async {
    await logEvent('af_share', {
      'af_content_type': contentType,
      'af_content_id': contentId,
      'af_channel': method ?? 'unknown',
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track tutorial completion
  static Future<void> logTutorialCompletion() async {
    await logEvent('af_tutorial_completion', {
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Track level achievement
  static Future<void> logLevelAchieved(int level) async {
    await logEvent('af_level_achieved', {
      'af_level': level.toString(),
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }
  
  /// Get AppsFlyer UID for cross-platform tracking
  static Future<String?> getAppsFlyerUID() async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('AppsFlyer not initialized');
      return null;
    }

    try {
      final uid = await _appsflyerSdk!.getAppsFlyerUID();
      debugPrint('🔍 conversions.appsflyersdk - Current AppsFlyer UID: $uid');
      return uid;
    } catch (e) {
      debugPrint('AppsFlyer: Failed to get UID: $e');
      return null;
    }
  }

  /// Force request conversion data (for debugging)
  static Future<void> requestConversionData() async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('AppsFlyer not initialized');
      return;
    }

    try {
      debugPrint('🔍 Requesting AppsFlyer conversion data...');
      // Note: There's no direct method to force conversion data request
      // The callback should trigger automatically on first app launch
      final uid = await getAppsFlyerUID();
      debugPrint('🔍 conversions.appsflyersdk - Install UID check: $uid');
    } catch (e) {
      debugPrint('Failed to request conversion data: $e');
    }
  }
  
  /// Handle install conversion data (first app open attribution)
  static void _handleInstallConversionData(Map<dynamic, dynamic> data) {
    try {
      final isFirstLaunch = data['is_first_launch'] as bool? ?? false;
      final status = data['af_status'] as String? ?? '';
      
      if (isFirstLaunch) {
        if (status == 'Non-organic') {
          // User came from a marketing campaign
          final mediaSource = data['media_source'] as String? ?? '';
          final campaign = data['campaign'] as String? ?? '';
          
          debugPrint('AppsFlyer: Non-organic install from $mediaSource, campaign: $campaign');
          
          // Log attribution data as custom event
          logEvent('af_first_launch_attribution', {
            'media_source': mediaSource,
            'campaign': campaign,
            'af_status': status,
          });
        } else {
          // Organic install
          debugPrint('AppsFlyer: Organic install');
          logEvent('af_first_launch_organic', {});
        }
      }
    } catch (e) {
      debugPrint('AppsFlyer: Error handling install conversion data: $e');
    }
  }
  
  /// Handle app open attribution (for re-engagement campaigns)
  static void _handleAppOpenAttribution(Map<dynamic, dynamic> data) {
    try {
      final mediaSource = data['media_source'] as String? ?? '';
      final campaign = data['campaign'] as String? ?? '';
      
      debugPrint('AppsFlyer: App opened from re-engagement campaign: $mediaSource');
      
      logEvent('af_app_open_attribution', {
        'media_source': mediaSource,
        'campaign': campaign,
      });
    } catch (e) {
      debugPrint('AppsFlyer: Error handling app open attribution: $e');
    }
  }
  
  /// Handle deep link data
  static void _handleDeepLink(DeepLinkResult result) {
    try {
      final deepLinkValue = result.deepLink?.deepLinkValue ?? '';
      final status = result.status.toString();

      debugPrint('AppsFlyer: Deep link received: $deepLinkValue, status: $status');

      if (result.status == Status.FOUND) {
        logEvent('af_deep_link', {
          'deep_link_value': deepLinkValue,
          'status': status,
        });

        // Handle deep link navigation here
        // You can use your app's navigation system to route users
        // Example: Navigate to specific screen based on deepLinkValue
      } else {
        debugPrint('AppsFlyer: Deep link not found or error: $status');
      }
    } catch (e) {
      debugPrint('AppsFlyer: Error handling deep link: $e');
    }
  }
  
  /// Enable/disable debug mode
  static void setDebugMode(bool enabled) {
    _isDebugMode = enabled;
    debugPrint('AppsFlyer debug mode: ${enabled ? 'enabled' : 'disabled'}');
  }
  
  /// Check if AppsFlyer is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Get SDK instance (for advanced usage)
  static AppsflyerSdk? get sdk => _appsflyerSdk;
}
