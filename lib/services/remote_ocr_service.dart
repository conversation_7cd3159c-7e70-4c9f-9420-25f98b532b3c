import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:imtrans/services/server_request.dart';
import 'package:imtrans/services/local_ocr_service.dart';
import 'package:http/http.dart' as http;

/// Remote OCR service using server-based OCR processing
/// 远程OCR服务，使用服务器端OCR处理
class RemoteOcrService {
  static final RemoteOcrService _instance = RemoteOcrService._internal();
  factory RemoteOcrService() => _instance;
  RemoteOcrService._internal();

  bool _isInitialized = false;
  
  /// 初始化远程OCR服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    debugPrint('RemoteOcrService: Initializing remote OCR service');
    _isInitialized = true;
    debugPrint('RemoteOcrService: Remote OCR service initialized');
  }

  /// 检查服务是否已初始化
  bool get isInitialized => _isInitialized;

  /// 处理图片OCR识别
  /// [imageUrls] 图片URL列表
  /// [extractor] 图片提取器（保持兼容性，但在远程OCR中不使用）
  /// [onProgress] 进度回调函数
  /// 返回识别到的文本元素列表
  Future<List<OcrTextElement>> processImagesWithOcr({
    required List<String> imageUrls,
    dynamic extractor,
    Function(String)? onProgress,
  }) async {
    if (!_isInitialized) {
      throw Exception('RemoteOcrService not initialized');
    }

    debugPrint('RemoteOcrService: Processing ${imageUrls.length} images with remote OCR');
    final List<OcrTextElement> allTextElements = [];

    for (int i = 0; i < imageUrls.length; i++) {
      final imageUrl = imageUrls[i];
      onProgress?.call('Processing image ${i + 1}/${imageUrls.length} with remote OCR...');
      
      try {
        debugPrint('RemoteOcrService: Processing image: $imageUrl');
        
        // 处理单个图片的OCR
        final textElements = await _processImageOcr(imageUrl, onProgress);
        allTextElements.addAll(textElements);
        
        debugPrint('RemoteOcrService: Found ${textElements.length} text elements in image $imageUrl');
      } catch (e) {
        debugPrint('RemoteOcrService: Error processing image $imageUrl: $e');
        // 继续处理下一张图片，不中断整个流程
        continue;
      }
    }

    debugPrint('RemoteOcrService: Total ${allTextElements.length} text elements found across all images');
    return allTextElements;
  }

  /// 处理单个图片的OCR识别
  /// [imageUrl] 图片URL
  /// [onProgress] 进度回调函数
  /// 返回识别到的文本元素列表
  Future<List<OcrTextElement>> _processImageOcr(String imageUrl, Function(String)? onProgress) async {
    try {
      // 步骤1: 下载图片数据
      onProgress?.call('Downloading image...');
      final imageData = await _downloadImage(imageUrl);
      if (imageData == null) {
        throw Exception('Failed to download image from URL: $imageUrl');
      }

      // 步骤2: 上传图片到服务器
      onProgress?.call('Uploading image to server...');
      final uploadedImageUrl = await _uploadImageToServer(imageData);
      if (uploadedImageUrl == null) {
        throw Exception('Failed to upload image to server');
      }

      debugPrint('RemoteOcrService: Image uploaded successfully: $uploadedImageUrl');

      // 步骤3: 创建OCR任务
      onProgress?.call('Creating OCR task...');
      final taskId = await _createOcrTask(uploadedImageUrl);
      if (taskId == null) {
        throw Exception('Failed to create OCR task');
      }

      debugPrint('RemoteOcrService: Created OCR task with ID: $taskId');

      // 步骤4: 轮询任务状态直到完成
      onProgress?.call('Processing OCR task...');
      final ocrResult = await _pollTaskResult(taskId, onProgress);
      if (ocrResult == null) {
        throw Exception('Failed to get OCR result');
      }

      // 步骤5: 解析OCR结果并转换为标准格式
      final textElements = _parseOcrResult(ocrResult);
      debugPrint('RemoteOcrService: Parsed ${textElements.length} text elements from OCR result');

      return textElements;
    } catch (e) {
      debugPrint('RemoteOcrService: Error in _processImageOcr: $e');
      rethrow;
    }
  }

  /// 创建OCR任务
  /// [imageUrl] 图片URL
  /// 返回任务ID
  Future<String?> _createOcrTask(String imageUrl) async {
    try {
      final requestData = {
        'task_type': 'OCR',
        'input': {
          'img_original': imageUrl,
        },
      };
      
      debugPrint('RemoteOcrService: Creating OCR task with data: $requestData');
      
      final response = await ServerRequest.postData(
        ServerRequest.createTask,
        requestData,
      );
      
      debugPrint('RemoteOcrService: Create task response: $response');
      
      if (response['code'] == 0) {
        return response['task_id'] as String?;
      } else {
        debugPrint('RemoteOcrService: Failed to create task - Code: ${response['code']}, Message: ${response['msg']}');
        return null;
      }
    } catch (e) {
      debugPrint('RemoteOcrService: Error creating OCR task: $e');
      return null;
    }
  }

  /// 轮询任务结果
  /// [taskId] 任务ID
  /// [onProgress] 进度回调函数
  /// 返回OCR结果数据
  Future<Map<String, dynamic>?> _pollTaskResult(String taskId, Function(String)? onProgress) async {
    const maxAttempts = 30; // 最大轮询次数
    const pollInterval = Duration(seconds: 2); // 轮询间隔
    
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        onProgress?.call('Checking OCR progress... (${attempt}/${maxAttempts})');
        
        final response = await ServerRequest.getData(
          ServerRequest.queryTask,
          {'task_id': taskId},
        );
        
        debugPrint('RemoteOcrService: Poll attempt $attempt - Response: $response');
        
        if (response['code'] == 0) {
          final task = response['task'];
          final status = task['status'];
          
          if (status == 1) {
            // 任务完成
            debugPrint('RemoteOcrService: Task completed successfully');
            return task;
          } else if (status == 2) {
            // 任务失败
            debugPrint('RemoteOcrService: Task failed');
            return null;
          } else {
            // 任务仍在处理中，继续轮询
            debugPrint('RemoteOcrService: Task still processing, status: $status');
          }
        } else {
          debugPrint('RemoteOcrService: Query task failed - Code: ${response['code']}, Message: ${response['msg']}');
        }
        
        // 如果不是最后一次尝试，等待后继续
        if (attempt < maxAttempts) {
          await Future.delayed(pollInterval);
        }
      } catch (e) {
        debugPrint('RemoteOcrService: Error polling task result (attempt $attempt): $e');
        if (attempt == maxAttempts) {
          rethrow;
        }
        await Future.delayed(pollInterval);
      }
    }
    
    debugPrint('RemoteOcrService: Task polling timeout after $maxAttempts attempts');
    return null;
  }

  /// 解析OCR结果并转换为标准格式
  /// [taskResult] 服务器返回的任务结果
  /// 返回标准化的文本元素列表
  List<OcrTextElement> _parseOcrResult(Map<String, dynamic> taskResult) {
    final List<OcrTextElement> textElements = [];
    
    try {
      final output = taskResult['output'];
      if (output == null) {
        debugPrint('RemoteOcrService: No output in task result');
        return textElements;
      }
      
      final resultText = output['result_text'];
      if (resultText == null || resultText is! List) {
        debugPrint('RemoteOcrService: No result_text or invalid format in output');
        return textElements;
      }
      
      for (final item in resultText) {
        if (item is! Map<String, dynamic>) continue;
        
        final text = item['text'] as String?;
        final bound = item['bound'] as List?;
        
        if (text == null || bound == null || bound.length != 4) {
          debugPrint('RemoteOcrService: Invalid text or bound data: $item');
          continue;
        }
        
        // 解析边界框坐标
        // 服务器返回格式: [{"x": 477, "y": 116}, {"x": 901, "y": 116}, {"x": 901, "y": 194}, {"x": 477, "y": 194}]
        // 转换为标准格式: left, top, right, bottom
        final List<double> xCoords = [];
        final List<double> yCoords = [];
        
        for (final point in bound) {
          if (point is Map<String, dynamic>) {
            final x = (point['x'] as num?)?.toDouble();
            final y = (point['y'] as num?)?.toDouble();
            if (x != null && y != null) {
              xCoords.add(x);
              yCoords.add(y);
            }
          }
        }
        
        if (xCoords.length == 4 && yCoords.length == 4) {
          final left = xCoords.reduce((a, b) => a < b ? a : b);
          final right = xCoords.reduce((a, b) => a > b ? a : b);
          final top = yCoords.reduce((a, b) => a < b ? a : b);
          final bottom = yCoords.reduce((a, b) => a > b ? a : b);
          
          final boundingBox = OcrBoundingBox(
            left: left,
            top: top,
            right: right,
            bottom: bottom,
          );
          
          final textElement = OcrTextElement(
            text: text,
            boundingBox: boundingBox,
            confidence: 1.0, // 服务器OCR结果默认置信度为1.0
          );
          
          textElements.add(textElement);
          debugPrint('RemoteOcrService: Parsed text element: "$text" at ($left, $top, $right, $bottom)');
        } else {
          debugPrint('RemoteOcrService: Invalid coordinate data for text: $text');
        }
      }
    } catch (e) {
      debugPrint('RemoteOcrService: Error parsing OCR result: $e');
    }
    
    return textElements;
  }

  /// 下载图片数据
  /// [imageUrl] 图片URL
  /// 返回图片的字节数据
  Future<Uint8List?> _downloadImage(String imageUrl) async {
    try {
      debugPrint('RemoteOcrService: Downloading image from: $imageUrl');

      final response = await http.get(Uri.parse(imageUrl));

      if (response.statusCode == 200) {
        debugPrint('RemoteOcrService: Image downloaded successfully, size: ${response.bodyBytes.length} bytes');
        return response.bodyBytes;
      } else {
        debugPrint('RemoteOcrService: Failed to download image, status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('RemoteOcrService: Error downloading image: $e');
      return null;
    }
  }

  /// 上传图片到服务器
  /// [imageData] 图片字节数据
  /// 返回上传后的图片URL
  Future<String?> _uploadImageToServer(Uint8List imageData) async {
    try {
      debugPrint('RemoteOcrService: Uploading image to server, size: ${imageData.length} bytes');

      final filename = 'ocr_image_${DateTime.now().microsecondsSinceEpoch}.jpg';
      final response = await ServerRequest.upload(
        ServerRequest.fileUpload,
        imageData,
        filename,
      );

      debugPrint('RemoteOcrService: Upload response: $response');

      if (response['code'] == 0 && response['file_url'] != null) {
        final uploadedUrl = response['file_url'] as String;
        debugPrint('RemoteOcrService: Image uploaded successfully: $uploadedUrl');
        return uploadedUrl;
      } else {
        debugPrint('RemoteOcrService: Upload failed - Code: ${response['code']}, Message: ${response['msg']}');
        return null;
      }
    } catch (e) {
      debugPrint('RemoteOcrService: Error uploading image: $e');
      return null;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    _isInitialized = false;
    debugPrint('RemoteOcrService: Disposed');
  }
}
