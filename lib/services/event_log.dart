import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:imtrans/services/account.dart';
import 'package:imtrans/services/appsflyer_service.dart';

/// Firebase 事件记录服务
/// 
/// 用于记录应用中的各种事件，方便后续分析用户行为和应用使用情况
class EventLogService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static final _firestore = FirebaseFirestore.instance;
  static bool _isInitialized = false;

  /// 初始化事件记录服务
  /// 
  /// 默认设置用户同意数据收集
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 默认设置用户同意数据收集
      await setAnalyticsConsent(isGranted: true);
      _isInitialized = true;
      debugPrint('EventLogService 初始化完成，默认开启数据收集');
    } catch (e) {
      debugPrint('EventLogService 初始化失败: $e');
    }
  }

  /// 记录屏幕访问事件
  static Future<void> logScreenView({required String screenName}) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenName,
      );
      debugPrint('记录屏幕访问: $screenName');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('screen_view', {
        'screen_name': screenName,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });
    } catch (e) {
      debugPrint('记录屏幕访问失败: $e');
    }
  }

  /// 记录网站访问事件
  static Future<void> logWebsiteVisit({required String url, String? method}) async {
    try {
      Uri? uri;
      String domain = '';
      try {
        uri = Uri.parse(url);
        domain = uri.host;
      } catch (e) {
        debugPrint('解析URL失败: $e');
      }
      await _analytics.logEvent(
        name: 'website_visit',
        parameters: {
          'url': url,
          'domain': domain,
          'method': method ?? 'navigation',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录网站访问: $url');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('website_visit', {
        'url': url,
        'domain': domain,
        'method': method ?? 'navigation',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });
      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = {
        "url": url,
        "timestamp": Timestamp.now(),
      };
      try {
        await _firestore.collection("website").doc(uid.toString()).collection('url').add(dataToSave);
        // print("数据成功写入 Firestore");
      } catch (e) {
        print("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录网站访问失败: $e');
    }
  }

  /// 记录用户登录事件
  static Future<void> logLogin({required String loginMethod}) async {
    try {
      // Firebase Analytics
      await _analytics.logLogin(loginMethod: loginMethod);
      debugPrint('记录用户登录: $loginMethod');

      // AppsFlyer tracking
      await AppsFlyerService.logLogin(loginMethod);
    } catch (e) {
      debugPrint('记录用户登录失败: $e');
    }
  }

  /// 记录用户注册事件
  static Future<void> logSignUp({required String signUpMethod}) async {
    try {
      // Firebase Analytics
      await _analytics.logSignUp(signUpMethod: signUpMethod);
      debugPrint('记录用户注册: $signUpMethod');

      // AppsFlyer tracking
      await AppsFlyerService.logRegistration(signUpMethod);
    } catch (e) {
      debugPrint('记录用户注册失败: $e');
    }
  }

  /// 记录内容查看事件
  static Future<void> logContentView({
    required String contentType,
    required String itemId,
    String? itemName,
  }) async {
    try {
      // Firebase Analytics
      await _analytics.logEvent(
        name: 'content_view',
        parameters: {
          'content_type': contentType,
          'item_id': itemId,
          if (itemName != null) 'item_name': itemName,
        },
      );
      debugPrint('记录内容查看: $contentType - $itemId');

      // AppsFlyer tracking
      await AppsFlyerService.logContentView(
        contentType: contentType,
        contentId: itemId,
        contentName: itemName,
      );
    } catch (e) {
      debugPrint('记录内容查看失败: $e');
    }
  }

  /// 记录搜索事件
  static Future<void> logSearch({required String searchTerm}) async {
    try {
      // Firebase Analytics
      await _analytics.logSearch(searchTerm: searchTerm);
      debugPrint('记录搜索: $searchTerm');

      // AppsFlyer tracking
      await AppsFlyerService.logSearch(searchTerm);
    } catch (e) {
      debugPrint('记录搜索失败: $e');
    }
  }

  /// 记录分享事件
  static Future<void> logShare({
    required String contentType,
    required String itemId,
    String? method,
  }) async {
    try {
      // Firebase Analytics
      await _analytics.logShare(
        contentType: contentType,
        itemId: itemId,
        method: method ?? 'unknown',
      );
      debugPrint('记录分享: $contentType - $itemId');

      // AppsFlyer tracking
      await AppsFlyerService.logShare(
        contentType: contentType,
        contentId: itemId,
        method: method,
      );
    } catch (e) {
      debugPrint('记录分享失败: $e');
    }
  }

  /// 记录进入购买页
  static Future<void> logEnterPurchase({
    required String userId,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'enter_purchase',
        parameters: {
          'user_id': userId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('enter_purchase', {
        'user_id': userId,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = {
        "user_id": userId,
        "timestamp": Timestamp.now(),
      };
      try {
        await _firestore.collection("purchase").doc(uid.toString()).collection('enter_purchase').add(dataToSave);
        debugPrint("数据成功写入 Firestore");
      } catch (e) {
        debugPrint("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录进入购买页失败: $e');
    }
  }

  /// 记录购买事件
  static Future<void> logPurchase({
    required double value,
    required String currency,
    String? itemId,
    String? itemName,
  }) async {
    try {
      // Firebase Analytics
      await _analytics.logPurchase(
        currency: currency,
        value: value,
        items: itemId != null ? [
          AnalyticsEventItem(
            itemId: itemId,
            itemName: itemName,
          ),
        ] : null,
      );
      debugPrint('记录购买: $value $currency - $itemName');

      // AppsFlyer tracking
      await AppsFlyerService.logPurchase(
        revenue: value,
        currency: currency,
        productId: itemId ?? '',
        productName: itemName ?? '',
      );
    } catch (e) {
      debugPrint('记录购买失败: $e');
    }
  }

  /// 记录图片翻译事件
  static Future<void> logImageTranslation({
    required String productId,
    required String targetLang,
    required bool success,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'image_translation',
        parameters: {
          'product_id': productId,
          'target_lang': targetLang,
          'success': success.toString(),
          if (errorMessage != null) 'error_message': errorMessage,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录图片翻译: $productId ($targetLang) -  [1m${success ? '成功' : '失败'}');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('image_translation', {
        'product_id': productId,
        'target_lang': targetLang,
        'success': success.toString(),
        if (errorMessage != null) 'error_message': errorMessage,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = {
        "product_id": productId,
        "target_lang": targetLang,
        "success": success.toString(),
        if (errorMessage != null) "error_message": errorMessage,
        "timestamp": Timestamp.now(),
      };
      try {
        await _firestore.collection("translation").doc(uid.toString()).collection('image').add(dataToSave);
        debugPrint("数据成功写入 Firestore");
      } catch (e) {
        debugPrint("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录图片翻译失败: $e');
    }
  }

  /// 记录漫画下载事件
  static Future<void> logMangaDownload(Map<String, dynamic> data) async {
    try {
      await _analytics.logEvent(
        name: 'manga_download',
        parameters: {
          'success_count': data['success_count'],
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录漫画下载');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('manga_download', {
        'success_count': data['success_count'].toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = data;
      try {
        await _firestore.collection("download").doc(uid.toString()).collection('manga').add(dataToSave);
        debugPrint("数据成功写入 Firestore");
      } catch (e) {
        debugPrint("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录漫画下载失败: $e');
    }
  }

  /// 记录用户反馈事件
  static Future<void> logFeedback({
    required String feedbackType,
    String? content,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'user_feedback',
        parameters: {
          'feedback_type': feedbackType,
          if (content != null) 'content': content,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录用户反馈: $feedbackType');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('user_feedback', {
        'feedback_type': feedbackType,
        if (content != null) 'content': content,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = {
        "feedback_type": feedbackType,
        if (content != null) "content": content,
        "timestamp": Timestamp.now(),
      };
      try {
        await _firestore.collection("feedback").doc(uid.toString()).collection('user').add(dataToSave);
        debugPrint("数据成功写入 Firestore");
      } catch (e) {
        debugPrint("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录用户反馈失败: $e');
    }
  }

  /// 记录自定义事件
  static Future<void> logCustomEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: parameters?.map((key, value) {
          if (value is bool) {
            return MapEntry(key, value.toString());
          }
          return MapEntry(key, value as Object);
        }),
      );
      debugPrint('记录自定义事件: $eventName - $parameters');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent(eventName, {
        ...?parameters?.map((key, value) => MapEntry(key, value.toString())),
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // 保存数据到 Firestore
      Account account = await Account.instance;
      final uid = account.info['uid'];
      Map<String, dynamic> dataToSave = {
        "event_name": eventName,
        ...?parameters,
        "timestamp": Timestamp.now(),
      };
      try {
        await _firestore.collection("custom_events").doc(uid.toString()).collection('events').add(dataToSave);
        debugPrint("数据成功写入 Firestore");
      } catch (e) {
        debugPrint("写入 Firestore 时出错: $e");
      }
    } catch (e) {
      debugPrint('记录自定义事件失败: $e');
    }
  }

  /// 设置用户属性
  static Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
      debugPrint('设置用户属性: $name = $value');
    } catch (e) {
      debugPrint('设置用户属性失败: $e');
    }
  }

  /// 设置用户对数据收集的同意
  /// 
  /// 若用户同意，则开启 Firebase Analytics 数据收集；反之关闭。
  static Future<void> setAnalyticsConsent({required bool isGranted}) async {
    try {
      await _analytics.setAnalyticsCollectionEnabled(isGranted);
      await _analytics.logEvent(
        name: 'analytics_consent',
        parameters: {
          'is_granted': isGranted.toString(),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('用户${isGranted ? '同意' : '拒绝'}数据收集');
    } catch (e) {
      debugPrint('设置用户同意失败: $e');
    }
  }

  /// 记录拍照事件
  static Future<void> logPhotoCapture({required bool success}) async {
    try {
      await _analytics.logEvent(
        name: 'photo_capture',
        parameters: {
          'success': success.toString(),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录拍照: ${success ? '成功' : '失败'}');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('photo_capture', {
        'success': success.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });
    } catch (e) {
      debugPrint('记录拍照失败: $e');
    }
  }

  /// 记录相册图片上传/选择事件
  static Future<void> logPhotoUpload({required int count}) async {
    try {
      await _analytics.logEvent(
        name: 'photo_upload',
        parameters: {
          'count': count,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录上传照片数量: $count');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('photo_upload', {
        'count': count.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });
    } catch (e) {
      debugPrint('记录上传照片失败: $e');
    }
  }

  /// 记录翻译开始事件
  static Future<void> logTranslateStart({
    required String targetLang,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'translate_start',
        parameters: {
          'target_lang': targetLang,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      debugPrint('记录翻译开始: $targetLang');
      // AppsFlyer 上报
      await AppsFlyerService.logEvent('translate_start', {
        'target_lang': targetLang,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });
    } catch (e) {
      debugPrint('记录翻译开始失败: $e');
    }
  }
}