import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// OCR偏好设置管理器
/// 管理用户对OCR处理方式的偏好设置
class OcrPreferences extends ChangeNotifier {
  static OcrPreferences? _instance;
  static OcrPreferences get instance => _instance ??= OcrPreferences._();
  
  OcrPreferences._();

  static const String _useServerOcrKey = 'use_server_ocr';
  
  bool _useServerOcr = false; // false = 本地OCR, true = 服务器OCR
  bool _initialized = false;
  
  /// 是否使用服务器OCR
  bool get useServerOcr => _useServerOcr;
  
  /// 是否已初始化
  bool get initialized => _initialized;
  
  /// 初始化OCR偏好设置
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      _useServerOcr = prefs.getBool(_useServerOcrKey) ?? false; // 默认使用本地OCR
      _initialized = true;
      debugPrint('OcrPreferences: Initialized with useServerOcr = $_useServerOcr');
    } catch (e) {
      debugPrint('OcrPreferences: Error initializing preferences: $e');
      _useServerOcr = false; // 出错时默认使用本地OCR
      _initialized = true;
    }
  }
  
  /// 设置OCR处理方式
  /// [useServer] true表示使用服务器OCR，false表示使用本地OCR
  Future<void> setUseServerOcr(bool useServer) async {
    if (_useServerOcr == useServer) return;
    
    _useServerOcr = useServer;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_useServerOcrKey, useServer);
      debugPrint('OcrPreferences: Set useServerOcr to $useServer');
    } catch (e) {
      debugPrint('OcrPreferences: Error saving preference: $e');
    }
    
    notifyListeners();
  }
  
  /// 获取当前OCR处理方式的描述
  String getOcrTypeDescription() {
    return _useServerOcr ? 'Server OCR' : 'Local OCR';
  }

  /// 重置状态（仅用于测试）
  @visibleForTesting
  void resetForTesting() {
    _initialized = false;
    _useServerOcr = false;
  }
}
