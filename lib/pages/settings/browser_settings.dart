import 'package:flutter/material.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/ocr_preferences.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BrowserSettingsPage extends StatefulWidget {
  const BrowserSettingsPage({Key? key}) : super(key: key);

  @override
  State<BrowserSettingsPage> createState() => _BrowserSettingsPageState();
}

class _BrowserSettingsPageState extends State<BrowserSettingsPage> {
  bool _isAdBlockingEnabled = true;
  bool _useServerOcr = false; // false = local OCR, true = server OCR
  static const String _adBlockingEnabledKey = 'ad_blocking_enabled';
  static const String _useServerOcrKey = 'use_server_ocr';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 确保 OcrPreferences 已初始化
    await OcrPreferences.instance.initialize();

    setState(() {
      _isAdBlockingEnabled = prefs.getBool(_adBlockingEnabledKey) ?? true; // 默认启用广告拦截
      _useServerOcr = OcrPreferences.instance.useServerOcr; // 从 OcrPreferences 获取当前设置
    });

    debugPrint('BrowserSettings: Loaded OCR preference: ${_useServerOcr ? "server" : "local"}');
  }

  // 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_adBlockingEnabledKey, _isAdBlockingEnabled);
    await prefs.setBool(_useServerOcrKey, _useServerOcr);
  }

  // 切换广告拦截状态
  Future<void> _toggleAdBlocking(bool value) async {
    setState(() {
      _isAdBlockingEnabled = value;
    });
    await _saveSettings();
  }

  // 切换OCR处理方式
  Future<void> _toggleOcrProcessing(bool value) async {
    setState(() {
      _useServerOcr = value;
    });
    await _saveSettings();

    // 同步更新 OcrPreferences 实例
    await OcrPreferences.instance.setUseServerOcr(value);
    debugPrint('BrowserSettings: OCR preference updated to ${value ? "server" : "local"}');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final themeColors = ThemeManager.currentTheme;

    return Theme(
      data: Theme.of(context).copyWith(
        switchTheme: SwitchThemeData(
          trackOutlineColor: WidgetStateProperty.all(Colors.transparent),
        ),
      ),
      child: Scaffold(
        backgroundColor: themeColors.backgroundColor,
        appBar: AppBar(
          backgroundColor: themeColors.backgroundColor,
          automaticallyImplyLeading: false,
          centerTitle: true,
          toolbarHeight: 60,
          titleSpacing: 0,
          title: Container(
            height: 60,
            width: double.infinity,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  l10n.browserSettings,
                  style: TextStyle(
                    color: themeColors.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Positioned(
                  left: 10,
                  top: 0,
                  bottom: 0,
                  child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Image.asset(
                      ThemeManager.getImagePath('btn_back'),
                      width: 40,
                      height: 40,
                    ),
                  ),
                ),
              ]),
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 添加说明文本
              Padding(
                padding: const EdgeInsets.only(left: 26, right: 16, top: 16, bottom: 10),
                child: Text(
                  l10n.adBlockingDescription,
                  style: TextStyle(
                    color: const Color(0xFF797979),
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration( 
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      // 广告拦截设置
                      Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: themeColors.borderAreaBgColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                l10n.adBlocking,
                                style: TextStyle(
                                  color: themeColors.textColor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Switch(
                              value: _isAdBlockingEnabled,
                              onChanged: _toggleAdBlocking,
                              activeColor: Colors.white,
                              activeTrackColor: themeColors.logoColor,
                              inactiveThumbColor: Colors.white,
                              inactiveTrackColor: Colors.grey.shade300,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // OCR处理设置
                      Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: themeColors.borderAreaBgColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                l10n.ocrProcessing,
                                style: TextStyle(
                                  color: themeColors.textColor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Switch(
                              value: _useServerOcr,
                              onChanged: _toggleOcrProcessing,
                              activeColor: Colors.white,
                              activeTrackColor: themeColors.logoColor,
                              inactiveThumbColor: Colors.white,
                              inactiveTrackColor: Colors.grey.shade300,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),

                      // OCR处理说明
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          _useServerOcr ? l10n.serverOcr : l10n.localOcr,
                          style: TextStyle(
                            color: themeColors.textColor.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 可以在这里添加更多浏览器设置项
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}